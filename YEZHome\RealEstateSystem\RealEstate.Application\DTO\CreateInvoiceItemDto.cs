using System.ComponentModel.DataAnnotations;
using static RealEstate.Domain.Common.EnumValues;

namespace RealEstate.Application.DTO
{
    public class CreateInvoiceItemDto
    {
        [Required]
        public InvoiceItemType ItemType { get; set; }
        
        public string? Description { get; set; }
        
        [Required]
        [Range(0, int.MaxValue, ErrorMessage = "Amount must be greater than or equal to 0")]
        public int Amount { get; set; }
    }
}
