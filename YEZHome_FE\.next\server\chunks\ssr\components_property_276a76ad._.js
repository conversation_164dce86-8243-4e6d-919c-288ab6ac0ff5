module.exports = {

"[project]/components/property/SearchFilter.jsx [app-ssr] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/components_66905b77._.js",
  "server/chunks/ssr/node_modules_9c0d2b73._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/components/property/SearchFilter.jsx [app-ssr] (ecmascript, next/dynamic entry)");
    });
});
}}),
"[project]/components/property/PropertyList.jsx [app-ssr] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/[root-of-the-server]__a9307d30._.js",
  "server/chunks/ssr/node_modules_ce40f631._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/components/property/PropertyList.jsx [app-ssr] (ecmascript, next/dynamic entry)");
    });
});
}}),

};