namespace RealEstate.Application.DTO
{
    public class InvoiceFilterDto
    {
        public Guid? UserId { get; set; }
        public Guid? PropertyId { get; set; }
        public string? Status { get; set; }
        public string? Type { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public int? MinAmount { get; set; }
        public int? MaxAmount { get; set; }
        public string? SearchTerm { get; set; }
        public string SortBy { get; set; } = "CreatedAt";
        public bool SortDescending { get; set; } = true;
        public int Page { get; set; } = 1;
        public int PageSize { get; set; } = 20;
    }
}
