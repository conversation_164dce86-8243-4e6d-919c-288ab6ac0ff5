using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using RealEstate.Application.DTO;
using RealEstate.Application.DTO.Analytics;
using RealEstate.Application.DTO.Wallet;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.Common;
using static RealEstate.Domain.Common.EnumValues;

namespace RealEstate.API.Controllers
{
    /// <summary>
    /// Controller for managing property listings and related operations
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    public class PropertyController : BaseController
    {
        private readonly IPropertyService _propertyService;
        private readonly IMediaServices _mediaServices;
        private readonly ILogger<PropertyController> _logger;
        private readonly IImageProcessingService _imageProcessingService;
        private readonly IPropertyAnalyticsService _propertyAnalyticsService;
        private readonly IWalletService _walletService;
        private readonly IUserDashboardService _userDashboardService;
        private readonly IUserService _userService;

        // Error message constants
        private const string UNAUTHORIZED_MESSAGE = "Bạn không có quyền thực hiện hành động này với bất động sản này";
        private const string INVALID_USER_MESSAGE = "User không hợp lệ";

        public PropertyController(IPropertyService propertyService, ILogger<PropertyController> logger, IMediaServices mediaServices, IImageProcessingService imageProcessingService, IPropertyAnalyticsService propertyAnalyticsService, IWalletService walletService, IUserDashboardService userDashboardService, IUserService userService)
        {
            _propertyService = propertyService;
            _logger = logger;
            _mediaServices = mediaServices;
            _imageProcessingService = imageProcessingService;
            _propertyAnalyticsService = propertyAnalyticsService;
            _walletService = walletService;
            _userDashboardService = userDashboardService;
            _userService = userService;
        }

        /// <summary>
        /// Get a property by its ID
        /// </summary>
        /// <param name="propertyId">The unique identifier of the property</param>
        /// <returns>The property details</returns>
        /// <response code="200">Returns the property</response>
        /// <response code="404">If the property is not found</response>
        [AllowAnonymous]
        [HttpGet("{propertyId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<PropertyDto>> GetPropertyById(Guid propertyId)
        {
            var property = await _propertyService.GetPropertyByIdAsync(propertyId);
            if (property == null)
            {
                return NotFound();
            }

            return Ok(property);
        }

        /// <summary>
        /// Get all properties
        /// </summary>
        /// <returns>List of all properties</returns>
        /// <response code="200">Returns the list of properties</response>
        [HttpGet]
        [AllowAnonymous]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<PropertyDto>>> GetAllProperties()
        {
            var properties = await _propertyService.GetAllPropertiesAsync();
            return Ok(properties);
        }

        /// <summary>
        /// Create a new property
        /// </summary>
        /// <param name="propertyDto">The property data</param>
        /// <returns>The created property</returns>
        /// <response code="200">Returns the newly created property</response>
        /// <response code="400">If the property data is invalid</response>
        /// <response code="401">If the user is not authenticated</response>
        [HttpPost]
        [Authorize(Policy = "UserExists")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<ActionResult<PropertyDto>> CreateProperty([FromBody] CreatePropertyDto propertyDto)
        {

            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var userId = GetUserId();
            if (userId == null)
            {
                return BadRequest(INVALID_USER_MESSAGE);
            }

            var createdProperty = await _propertyService.CreatePropertyAsync(propertyDto, userId.Value);

            // Di chuyển hình ảnh từ Temp sang thư mục chính và cập nhật Caption, IsAvatar
            var mediaIds = propertyDto.UploadedFiles?.Select(x => x.Id).ToList();
            if (mediaIds != null)
            {
                await MoveTempImagesToPropertyFolder(createdProperty.Id, mediaIds, propertyDto.UploadedFiles);
            }

            return Ok(createdProperty);
        }

        /// <summary>
        /// Update an existing property
        /// </summary>
        /// <param name="propertyId">The ID of the property to update</param>
        /// <param name="propertyDto">The updated property data</param>
        /// <returns>The updated property</returns>
        /// <response code="201">Returns the updated property</response>
        /// <response code="400">If the property data is invalid</response>
        /// <response code="401">If the user is not authenticated</response>
        /// <response code="403">If the user is not authorized to update this property</response>
        /// <response code="404">If the property is not found</response>
        [HttpPut("{propertyId}")]
        [Authorize(Policy = "UserExists")]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> UpdateProperty(Guid propertyId, [FromBody] CreatePropertyDto propertyDto)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            try
            {
                var userId = GetUserId();
                if (userId == null)
                {
                    return BadRequest(INVALID_USER_MESSAGE);
                }

                // Check if the user is authorized to update this property
                var isAuthorized = await IsUserAuthorizedForProperty(propertyId, userId.Value);
                if (!isAuthorized)
                {
                    return Forbid(UNAUTHORIZED_MESSAGE);
                }

                var updatedProperty = await _propertyService.UpdatePropertyAsync(propertyId, propertyDto, userId.Value);

                // Cập nhật thông tin Caption và IsAvatar cho các file
                if (propertyDto.UploadedFiles != null && propertyDto.UploadedFiles.Any())
                {
                    await UpdateExistingMediaFiles(propertyId, propertyDto.UploadedFiles);
                }

                // Fetch the complete property data including updated media
                var completeUpdatedProperty = await _propertyService.GetPropertyByIdAsync(propertyId, false);

                return CreatedAtAction(nameof(GetPropertyById), new { propertyId = completeUpdatedProperty.Id }, completeUpdatedProperty);
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error when UpdateProperty");
                return StatusCode(500, new { Message = "Đã xảy ra lỗi trong quá trình xử lý. Vui lòng thử lại sau." });
            }
        }

        /// <summary>
        /// Delete a property
        /// </summary>
        /// <param name="propertyId">The ID of the property to delete</param>
        /// <returns>No content if successful</returns>
        /// <response code="204">If the property was successfully deleted</response>
        /// <response code="401">If the user is not authenticated</response>
        /// <response code="403">If the user is not authorized to delete this property</response>
        /// <response code="404">If the property is not found</response>
        [HttpDelete("{propertyId}")]
        [Authorize(Policy = "UserExists")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteProperty(Guid propertyId)
        {
            try
            {
                var userId = GetUserId();
                if (userId == null)
                {
                    return BadRequest(INVALID_USER_MESSAGE);
                }

                // Check if the user is authorized to delete this property
                var isAuthorized = await IsUserAuthorizedForProperty(propertyId, userId.Value);
                if (!isAuthorized)
                {
                    return Forbid(UNAUTHORIZED_MESSAGE);
                }

                await _propertyService.DeletePropertyAsync(propertyId, userId.Value);
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
            return NoContent();
        }

        /// <summary>
        /// Delete multiple properties in bulk
        /// </summary>
        /// <param name="request">The list of property IDs to delete</param>
        /// <returns>No content if successful</returns>
        /// <response code="204">If the properties were successfully deleted</response>
        /// <response code="400">If the request is invalid or some properties couldn't be deleted</response>
        /// <response code="401">If the user is not authenticated</response>
        /// <response code="403">If the user is not authorized to delete one or more properties</response>
        [HttpDelete("bulk")]
        [Authorize(Policy = "UserExists")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> DeleteProperties([FromBody] BulkPropertyIdsDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var userId = GetUserId();
                if (userId == null)
                {
                    return BadRequest(INVALID_USER_MESSAGE);
                }

                // Check if the user is authorized to delete all these properties
                var isAuthorized = await IsUserAuthorizedForAllProperties(request.PropertyIds, userId.Value);
                if (!isAuthorized)
                {
                    return Forbid(UNAUTHORIZED_MESSAGE);
                }

                var result = await _propertyService.DeletePropertiesAsync(request.PropertyIds, userId.Value);
                if (!result)
                {
                    return BadRequest("Một số bất động sản không tồn tại hoặc không thể xóa");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error when DeleteProperties");
                return StatusCode(500, new { Message = "Đã xảy ra lỗi trong quá trình xử lý. Vui lòng thử lại sau." });
            }
            return NoContent();
        }

        /// <summary>
        /// Update the status of a property
        /// </summary>
        /// <param name="propertyId">The ID of the property to update</param>
        /// <param name="request">The status update information</param>
        /// <returns>No content if successful</returns>
        /// <response code="204">If the status was successfully updated</response>
        /// <response code="400">If the request is invalid</response>
        /// <response code="401">If the user is not authenticated</response>
        /// <response code="403">If the user is not authorized to update this property</response>
        /// <response code="404">If the property is not found</response>
        [HttpPut("{propertyId}/status")]
        [Authorize(Policy = "UserExists")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> UpdateStatus(Guid propertyId, [FromBody] UpdateStatusDto request)
        {
            // 1. Validate Request
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            try
            {
                var userId = GetUserId();
                if (userId == null)
                {
                    return BadRequest(INVALID_USER_MESSAGE);
                }

                // 2. Retrieve the existing entity
                var property = await _propertyService.GetPropertyByIdAsync(propertyId);
                if (property == null)
                {
                    return NotFound();
                }

                // 3. Check if the user is authorized to update this property
                var isAuthorized = await IsUserAuthorizedForProperty(propertyId, userId.Value);
                if (!isAuthorized)
                {
                    return Forbid(UNAUTHORIZED_MESSAGE);
                }

                // 4. Save changes to the database
                await _propertyService.UpdateStatusAsync(propertyId, userId.Value, request);

            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
            // 5. Return success response
            return NoContent();
        }

        /// <summary>
        /// Update the status of multiple properties in bulk
        /// </summary>
        /// <param name="request">The bulk status update information containing property IDs, status, and comment</param>
        /// <returns>No content if successful</returns>
        /// <response code="204">If the statuses were successfully updated</response>
        /// <response code="400">If the request is invalid or some properties couldn't be updated</response>
        /// <response code="401">If the user is not authenticated</response>
        /// <response code="403">If the user is not authorized to update one or more properties</response>
        [HttpPut("bulk/status")]
        [Authorize(Policy = "UserExists")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> UpdateStatusBulk([FromBody] BulkUpdateStatusDto request)
        {
            // 1. Validate Request
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            try
            {
                var userId = GetUserId();
                if (userId == null)
                {
                    return BadRequest(INVALID_USER_MESSAGE);
                }

                // Check if the user is authorized to update all these properties
                var isAuthorized = await IsUserAuthorizedForAllProperties(request.PropertyIds, userId.Value);
                if (!isAuthorized)
                {
                    return Forbid(UNAUTHORIZED_MESSAGE);
                }

                // Create UpdateStatusDto from the bulk request
                var updateStatusDto = new UpdateStatusDto
                {
                    Status = request.Status,
                    Comment = request.Comment
                };

                // Update status for all properties
                var result = await _propertyService.UpdateStatusBulkAsync(request.PropertyIds, userId.Value, updateStatusDto);
                if (!result)
                {
                    return BadRequest("Một số bất động sản không tồn tại hoặc không thể cập nhật trạng thái");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error when UpdateStatusBulk");
                return StatusCode(500, new { Message = "Đã xảy ra lỗi trong quá trình xử lý. Vui lòng thử lại sau." });
            }
            return NoContent();
        }

        /// <summary>
        /// Update the highlight status of a property
        /// </summary>
        /// <param name="propertyId">The ID of the property to update</param>
        /// <param name="request">The highlight update information</param>
        /// <returns>The updated property if successful</returns>
        /// <response code="200">If the highlight status was successfully updated</response>
        /// <response code="400">If the request is invalid or the user has insufficient funds</response>
        /// <response code="401">If the user is not authenticated</response>
        /// <response code="403">If the user is not authorized to update this property</response>
        /// <response code="404">If the property is not found</response>
        [HttpPut("{propertyId}/highlight")]
        [Authorize(Policy = "UserExists")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<PropertyDto>> UpdateHighlight(Guid propertyId, [FromBody] UpdateHighlightDto request)
        {
            // 1. Validate Request
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            try
            {
                var userId = GetUserId();
                if (userId == null)
                {
                    return BadRequest(INVALID_USER_MESSAGE);
                }

                // 2. Retrieve the existing entity
                var property = await _propertyService.GetPropertyByIdAsync(propertyId);
                if (property == null)
                {
                    return NotFound("Bất động sản không tồn tại");
                }

                // 3. Check if the user is authorized to update this property
                var isAuthorized = await IsUserAuthorizedForProperty(propertyId, userId.Value);
                if (!isAuthorized)
                {
                    return Forbid(UNAUTHORIZED_MESSAGE);
                }

                // 4. If setting highlight to true and property is not already highlighted, process payment
                if (!property.IsHighlighted)
                {
                    // 4.1 Get the highlight fee based on the user's membership rank
                    decimal fee;
                    try
                    {
                        fee = await _userDashboardService.GetHighlightFeeByUserIdAsync(userId.Value);
                    }
                    catch (KeyNotFoundException)
                    {
                        return BadRequest("Không thể xác định người dùng");
                    }
                    catch (InvalidOperationException)
                    {
                        return StatusCode(500, "Không thể xác định phí đánh dấu nổi bật");
                    }

                    // 4.2 Check if user has sufficient wallet balance
                    var walletBalance = await _walletService.GetWalletBalanceAsync(userId.Value);
                    if (walletBalance.Balance < fee)
                    {
                        return BadRequest($"Số dư ví không đủ. Cần {fee:N0} VND để đánh dấu nổi bật.");
                    }

                    // 4.3 Create wallet transaction for the highlight fee
                    var spendRequest = new SpendWalletDto
                    {
                        Amount = fee,
                        Description = $"Đánh dấu nổi bật bất động sản #{property.Id}"
                    };

                    var transaction = await _walletService.SpendFromWalletAsync(userId.Value, spendRequest);

                    // 4.4 Process the transaction immediately
                    var processRequest = new ProcessTransactionDto
                    {
                        TransactionId = transaction.Id,
                        Action = "complete"
                    };

                    await _walletService.ProcessTransactionAsync(transaction.Id, processRequest, userId.Value);

                    // 4.5 Update property highlight status
                    var result = await _propertyService.UpdateHighlightAsync(propertyId, userId.Value, true);
                    if (!result)
                    {
                        return BadRequest("Không thể cập nhật trạng thái nổi bật");
                    }


                }
                else
                {
                    // Property is already highlighted, no need to do anything
                    return BadRequest("Bất động sản đã được đánh dấu nổi bật");
                }

                // 6. Return the updated property
                var updatedProperty = await _propertyService.GetPropertyByIdAsync(propertyId);
                return Ok(updatedProperty);
            }
            catch (KeyNotFoundException)
            {
                return NotFound();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error when UpdateHighlight");
                return StatusCode(500, new { Message = "Đã xảy ra lỗi trong quá trình xử lý. Vui lòng thử lại sau." });
            }
        }

        /// <summary>
        /// Update the highlight status of multiple properties in bulk
        /// </summary>
        /// <param name="request">The bulk highlight update information containing property IDs and highlight status</param>
        /// <returns>No content if successful</returns>
        /// <response code="204">If the highlight statuses were successfully updated</response>
        /// <response code="400">If the request is invalid or some properties couldn't be updated</response>
        /// <response code="401">If the user is not authenticated</response>
        /// <response code="403">If the user is not authorized to update one or more properties</response>
        [HttpPut("bulk/highlight")]
        [Authorize(Policy = "UserExists")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        public async Task<IActionResult> UpdateHighlightBulk([FromBody] BulkUpdateHighlightDto request)
        {
            // 1. Validate Request
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            try
            {
                var userId = GetUserId();
                if (userId == null)
                {
                    return BadRequest(INVALID_USER_MESSAGE);
                }

                // Check if the user is authorized to update all these properties
                var isAuthorized = await IsUserAuthorizedForAllProperties(request.PropertyIds, userId.Value);
                if (!isAuthorized)
                {
                    return Forbid(UNAUTHORIZED_MESSAGE);
                }

                // Note: For bulk operations, we don't process payments for highlighting
                // This is because it would be complex to handle partial successes and failures
                // The client should use the single property endpoint for payment processing

                // Update highlight status for all properties
                var result = await _propertyService.UpdateHighlightBulkAsync(request.PropertyIds, userId.Value, request.IsHighlighted);
                if (!result)
                {
                    return BadRequest("Một số bất động sản không tồn tại hoặc không thể cập nhật trạng thái nổi bật");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error when UpdateHighlightBulk");
                return StatusCode(500, new { Message = "Đã xảy ra lỗi trong quá trình xử lý. Vui lòng thử lại sau." });
            }
            return NoContent();
        }

        /// <summary>
        /// Get all properties owned by the current user
        /// </summary>
        /// <param name="status">Optional filter by property status</param>
        /// <param name="page">Page number for pagination</param>
        /// <param name="pageSize">Number of items per page</param>
        /// <returns>Paged list of properties owned by the current user</returns>
        /// <response code="200">Returns the list of properties</response>
        /// <response code="400">If the user is invalid</response>
        /// <response code="401">If the user is not authenticated</response>
        [HttpGet("me")]
        [Authorize(Policy = "UserExists")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<ActionResult<PagedResultDto<PropertyDto>>> GetAllPropertiesByUser(
            [FromQuery] List<string> status = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10)
        {
            var userId = GetUserId();
            if (userId == null)
            {
                return BadRequest(INVALID_USER_MESSAGE);
            }

            var properties = await _propertyService.GetPropertyByUserWithStatusAsync(userId.Value, status, page, pageSize);
            return Ok(properties);
        }

        /// <summary>
        /// Upload images for a property
        /// </summary>
        /// <param name="propertyId">Optional ID of the property to associate with the images</param>
        /// <param name="files">The image files to upload</param>
        /// <returns>List of uploaded property media</returns>
        /// <response code="200">Returns the list of uploaded media</response>
        /// <response code="400">If the request is invalid or no files were uploaded</response>
        /// <response code="401">If the user is not authenticated</response>
        /// <response code="404">If the property ID is provided but not found</response>
        [HttpPost("upload-images")]
        [Authorize(Policy = "UserExists")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<IEnumerable<PropertyMediaDto>>> UploadPropertyImages([FromForm]Guid? propertyId, IFormFileCollection files)
        {
            if (files == null || files.Count == 0)
            {
                return BadRequest("No files uploaded.");
            }

            var userId = GetUserId();
            if (userId == null)
            {
                return BadRequest(INVALID_USER_MESSAGE);
            }

            string uploadFolder;
            string finalFolder;
            if (propertyId != null && propertyId != Guid.Empty)
            {
                var property = await _propertyService.GetPropertyByIdAsync((Guid)propertyId);
                if (property == null)
                {
                    return NotFound();
                }
                uploadFolder = Path.Combine("PropertyImages", propertyId?.ToString() ?? string.Empty);
                finalFolder = uploadFolder;
            }
            else
            {
                uploadFolder = Path.Combine("Temp", DateTime.Today.ToString("yyyyMMdd"));
                finalFolder = uploadFolder;
            }

            if (!Directory.Exists(uploadFolder))
            {
                Directory.CreateDirectory(uploadFolder);
            }

            var uploadedFiles = new List<PropertyMediaDto>();

            foreach (var file in files)
            {
                // Skip non-image files
                if (!file.ContentType.StartsWith("image/"))
                {
                    continue;
                }

                // Generate a unique file ID (UUID-based)
                var fileId = Guid.NewGuid();
                var extension = Path.GetExtension(file.FileName);
                var tempFilePath = Path.Combine(uploadFolder, $"temp_{fileId}{extension}");

                // Save the original file temporarily
                using (var stream = new FileStream(tempFilePath, FileMode.Create))
                {
                    await file.CopyToAsync(stream);
                }

                try
                {
                    // Process the image to create different sizes with watermark
                    var baseFilename = fileId.ToString();
                    var watermarkText = "YEZ HOME"; // You could customize this based on your site name or property info

                    var processedImages = await _imageProcessingService.GenerateImageSizesAsync(
                        tempFilePath,
                        finalFolder,
                        baseFilename,
                        addWatermark: true,
                        watermarkText: watermarkText
                    );

                    // Generate a public URL (No file name in URL)
                    string publicUrl = $"{Request.Scheme}://{Request.Host}/media/{fileId}";

                    // Store metadata in the database
                    var media = new CreateMediaDto
                    {
                        Id = fileId,
                        PropertyID = propertyId,
                        MediaType = file.ContentType,
                        MediaURL = publicUrl,
                        FilePath = processedImages["original"], // Store path to original (but processed) image
                        ThumbnailPath = processedImages["thumbnail"],
                        SmallPath = processedImages["small"],
                        MediumPath = processedImages["medium"],
                        LargePath = processedImages["large"],
                        UploadedAt = DateTime.UtcNow
                    };

                    await _mediaServices.CreateMediaAsync(media);

                    var mediaDto = new PropertyMediaDto
                    {
                        Id = fileId,
                        PropertyID = propertyId,
                        MediaType = file.ContentType,
                        MediaURL = publicUrl,
                        ThumbnailURL = $"{Request.Scheme}://{Request.Host}/media/{fileId}?size=thumbnail",
                        SmallURL = $"{Request.Scheme}://{Request.Host}/media/{fileId}?size=small",
                        MediumURL = $"{Request.Scheme}://{Request.Host}/media/{fileId}?size=medium",
                        LargeURL = $"{Request.Scheme}://{Request.Host}/media/{fileId}?size=large",
                        UploadedAt = DateTime.UtcNow
                    };

                    uploadedFiles.Add(mediaDto);

                    if (System.IO.File.Exists(tempFilePath))
                    // Delete the temporary file
                    if (System.IO.File.Exists(tempFilePath))
                    {
                        System.IO.File.Delete(tempFilePath);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing image {FileId}", fileId);

                    // If processing fails, at least save the original
                    string originalFilePath = Path.Combine(finalFolder, fileId + extension);
                    System.IO.File.Move(tempFilePath, originalFilePath);

                    string publicUrl = $"{Request.Scheme}://{Request.Host}/media/{fileId}";

                    var media = new CreateMediaDto
                    {
                        Id = fileId,
                        PropertyID = propertyId,
                        MediaType = file.ContentType,
                        MediaURL = publicUrl,
                        FilePath = originalFilePath,
                        UploadedAt = DateTime.UtcNow
                    };

                    await _mediaServices.CreateMediaAsync(media);

                    var mediaDto = new PropertyMediaDto
                    {
                        Id = fileId,
                        MediaType = file.ContentType,
                        MediaURL = publicUrl,
                        UploadedAt = DateTime.UtcNow
                    };

                    uploadedFiles.Add(mediaDto);
                }
            }

            return Ok(uploadedFiles);
        }

        /// <summary>
        /// Get the number of remaining edit times for a property
        /// </summary>
        /// <param name="propertyId">The ID of the property</param>
        /// <returns>The number of remaining edit times</returns>
        /// <response code="200">Returns the remaining edit times</response>
        /// <response code="401">If the user is not authenticated</response>
        [HttpGet("edit-remaining/{propertyId}")]
        [Authorize(Policy = "UserExists")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<ActionResult<IEnumerable<PropertyDto>>> VerifyPropertyRemainingTimes(Guid propertyId)
        {
            var numUpdate = await _propertyService.VerifyPropertyRemainingTimes(propertyId);
            return Ok(numUpdate);
        }

        /// <summary>
        /// Get property count statistics for the current user
        /// </summary>
        /// <returns>Property count statistics</returns>
        /// <response code="200">Returns the property statistics</response>
        /// <response code="400">If the user is invalid</response>
        /// <response code="401">If the user is not authenticated</response>
        [HttpGet("stats")]
        [Authorize(Policy = "UserExists")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        public async Task<ActionResult<PropertyCountStatsDto>> GetPropertyCountStats()
        {
            var userId = GetUserId();
            if (userId == null)
            {
                return BadRequest(INVALID_USER_MESSAGE);
            }

            var stats = await _propertyService.GetPropertyCountStatsByUserAsync(userId.Value);
            return Ok(stats);
        }

        /// <summary>
        /// Search for properties based on various criteria
        /// </summary>
        /// <param name="postType">Filter by post type (e.g., Sell, Rent)</param>
        /// <param name="propertyType">Filter by property type (e.g., Apartment, House)</param>
        /// <param name="cityId">Filter by city ID</param>
        /// <param name="districtId">Filter by district ID</param>
        /// <param name="address">Filter by address text</param>
        /// <param name="minPrice">Filter by minimum price</param>
        /// <param name="maxPrice">Filter by maximum price</param>
        /// <param name="minArea">Filter by minimum area</param>
        /// <param name="maxArea">Filter by maximum area</param>
        /// <param name="minRooms">Filter by minimum number of rooms</param>
        /// <param name="minToilets">Filter by minimum number of toilets</param>
        /// <param name="direction">Filter by property direction</param>
        /// <param name="legality">Filter by property legality status</param>
        /// <param name="minRoadWidth">Filter by minimum road width</param>
        /// <param name="latitude">Center latitude for radius search</param>
        /// <param name="longitude">Center longitude for radius search</param>
        /// <param name="radius">Radius in kilometers for location-based search</param>
        /// <param name="page">Page number for pagination</param>
        /// <param name="pageSize">Number of items per page</param>
        /// <returns>Paged list of properties matching the criteria</returns>
        /// <response code="200">Returns the list of properties</response>
        /// <response code="500">If there was an error processing the request</response>
        [HttpGet("search")]
        [AllowAnonymous]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<PagedResultDto<PropertyDto>>> SearchProperties(
            [FromQuery] List<string>? postType = null,
            [FromQuery] List<string>? propertyType = null,
            [FromQuery] string? cityId = null,
            [FromQuery] string? districtId = null,
            [FromQuery] string? address = null,
            [FromQuery] decimal? minPrice = null,
            [FromQuery] decimal? maxPrice = null,
            [FromQuery] decimal? minArea = null,
            [FromQuery] decimal? maxArea = null,
            [FromQuery] int? minRooms = null,
            [FromQuery] int? minToilets = null,
            [FromQuery] string? direction = null,
            [FromQuery] string? legality = null,
            [FromQuery] decimal? minRoadWidth = null,
            [FromQuery] decimal? swLat = null,
            [FromQuery] decimal? swLng = null,
            [FromQuery] decimal? neLat = null,
            [FromQuery] decimal? neLng = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10)
        {
            try
            {
                var filterCriteria = new PropertyFilterCriteriaDto
                {
                    PostTypes = postType,
                    PropertyTypes = propertyType,
                    CityId = cityId,
                    DistrictId = districtId,
                    Address = address,
                    MinPrice = minPrice,
                    MaxPrice = maxPrice,
                    MinArea = minArea,
                    MaxArea = maxArea,
                    MinRooms = minRooms,
                    MinToilets = minToilets,
                    Direction = direction,
                    Legality = legality,
                    MinRoadWidth = minRoadWidth,
                    NeLat = neLat,
                    NeLng = neLng,
                    SwLat = swLat,
                    SwLng = swLng,
                    Page = page,
                    PageSize = pageSize
                };

                var result = await _propertyService.SearchPropertiesAsync(filterCriteria);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while searching properties");
                return StatusCode(500, new { Message = "Đã xảy ra lỗi trong quá trình tìm kiếm. Vui lòng thử lại sau." });
            }
        }

        /// <summary>
        /// Get properties near a specific location
        /// </summary>
        /// <param name="latitude">The latitude of the center point</param>
        /// <param name="longitude">The longitude of the center point</param>
        /// <param name="radius">The search radius in kilometers (default: 5km)</param>
        /// <param name="page">Page number for pagination</param>
        /// <param name="pageSize">Number of items per page</param>
        /// <returns>Paged list of properties within the specified radius</returns>
        /// <response code="200">Returns the list of nearby properties</response>
        /// <response code="500">If there was an error processing the request</response>
        [HttpGet("nearby")]
        [AllowAnonymous]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<PagedResultDto<PropertyDto>>> GetNearbyProperties(
            [FromQuery] decimal? swLat = null,
            [FromQuery] decimal? swLng = null,
            [FromQuery] decimal? neLat = null,
            [FromQuery] decimal? neLng = null,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10)
        {
            try
            {
                var filterCriteria = new PropertyFilterCriteriaDto
                {
                    SwLat = swLat,
                    SwLng = swLng,
                    NeLat = neLat,
                    NeLng = neLng,
                    Page = page,
                    PageSize = pageSize
                };

                var result = await _propertyService.SearchPropertiesAsync(filterCriteria);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while searching nearby properties");
                return StatusCode(500, new { Message = "Đã xảy ra lỗi trong quá trình tìm kiếm. Vui lòng thử lại sau." });
            }
        }


        /// <summary>
        /// Renew a property listing by extending its expiration date
        /// </summary>
        /// <param name="request">The renewal request containing property ID and duration</param>
        /// <returns>The updated property with new expiration date</returns>
        /// <response code="200">Returns the updated property</response>
        /// <response code="400">If the request is invalid or the user has insufficient funds</response>
        /// <response code="401">If the user is not authenticated</response>
        /// <response code="403">If the user is not authorized to renew this property</response>
        /// <response code="404">If the property is not found</response>
        [HttpPost("renew")]
        [Authorize(Policy = "UserExists")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<PropertyDto>> RenewProperty([FromBody] PropertyRenewalDto request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            try
            {
                var userId = GetUserId();
                if (userId == null)
                {
                    return BadRequest(INVALID_USER_MESSAGE);
                }

                // 1. Check if the property exists and get its details
                var property = await _propertyService.GetPropertyByIdAsync(request.PropertyId);
                if (property == null)
                {
                    return NotFound("Bất động sản không tồn tại");
                }

                // 2. Check if the user is authorized to renew this property
                var isAuthorized = await IsUserAuthorizedForProperty(request.PropertyId, userId.Value);
                if (!isAuthorized)
                {
                    return Forbid(UNAUTHORIZED_MESSAGE);
                }

                // 3. Check if the property is active (not expired or deleted)
                if (property.Status == EnumValues.PropertyStatus.Expired.ToString() ||
                    property.Status == EnumValues.PropertyStatus.Sold.ToString())
                {
                    return BadRequest("Không thể gia hạn bất động sản đã hết hạn hoặc đã bán");
                }

                // 4. Validate renewal duration (must be 10, 30, 60, or 90 days)
                if (request.DurationInDays != 10 && request.DurationInDays != 30 &&
                    request.DurationInDays != 60 && request.DurationInDays != 90)
                {
                    return BadRequest("Thời gian gia hạn không hợp lệ. Vui lòng chọn 10, 30, 60 hoặc 90 ngày.");
                }

                // 5. Calculate renewal fee (55,000 VND per 10-day block)
                decimal feePerBlock = 55000;
                int blocks = request.DurationInDays / 10;
                decimal totalFee = blocks * feePerBlock;

                // 6. Check if user has sufficient wallet balance
                var walletBalance = await _walletService.GetWalletBalanceAsync(userId.Value);
                if (walletBalance.Balance < totalFee)
                {
                    return BadRequest($"Số dư ví không đủ. Cần {totalFee:N0} VND để gia hạn.");
                }

                // 7. Create wallet transaction for the renewal fee
                var spendRequest = new SpendWalletDto
                {
                    Amount = totalFee,
                    Description = $"Gia hạn bất động sản #{property.Id} thêm {request.DurationInDays} ngày"
                };

                var transaction = await _walletService.SpendFromWalletAsync(userId.Value, spendRequest);

                // 8. Process the transaction immediately
                var processRequest = new ProcessTransactionDto
                {
                    TransactionId = transaction.Id,
                    Action = "complete"
                };

                await _walletService.ProcessTransactionAsync(transaction.Id, processRequest, userId.Value);

                // 9. Update property expiration date
                var currentExpiration = property.ExpiresAt;
                var newExpiration = currentExpiration > DateTime.UtcNow
                    ? currentExpiration.AddDays(request.DurationInDays)
                    : DateTime.UtcNow.AddDays(request.DurationInDays);

                // 10. Update property status if it was expired
                string newStatus = property.Status;
                if (property.Status == EnumValues.PropertyStatus.Expired.ToString())
                {
                    newStatus = EnumValues.PropertyStatus.Approved.ToString();
                }

                // 11. Create status update DTO
                var updateStatusDto = new UpdateStatusDto
                {
                    Status = newStatus,
                    Comment = $"Gia hạn thêm {request.DurationInDays} ngày"
                };

                // 12. Update property status and expiration date
                await _propertyService.UpdateStatusAsync(request.PropertyId, userId.Value, updateStatusDto);

                // 13. Create a DTO to update the property with new expiration date and renewal count
                var updatePropertyDto = new UpdatePropertyRenewalDto
                {
                    PropertyId = request.PropertyId,
                    ExpiresAt = newExpiration,
                    IncrementRenewalCount = true
                };

                // Update the property through the service
                await _propertyService.UpdatePropertyRenewalAsync(updatePropertyDto, userId.Value);

                // 14. Return the updated property
                var result = await _propertyService.GetPropertyByIdAsync(request.PropertyId);
                return Ok(result);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error when renewing property {PropertyId}", request.PropertyId);
                return StatusCode(500, new { Message = "Đã xảy ra lỗi trong quá trình xử lý. Vui lòng thử lại sau." });
            }
        }

        /// <summary>
        /// Checks if the current user is the owner of the property or an admin
        /// </summary>
        /// <param name="propertyId">The ID of the property to check</param>
        /// <param name="userId">The ID of the current user</param>
        /// <returns>True if the user is the owner or an admin, false otherwise</returns>
        private async Task<bool> IsUserAuthorizedForProperty(Guid propertyId, Guid userId)
        {
            // Check if user is admin (you may need to implement this based on your authorization system)
            bool isAdmin = User.IsInRole("Admin");
            if (isAdmin)
            {
                return true;
            }

            // Check if user is the owner of the property
            var property = await _propertyService.GetPropertyByIdAsync(propertyId);
            if (property == null)
            {
                return false;
            }

            return property.OwnerId == userId;
        }

        /// <summary>
        /// Checks if the current user is the owner of all the properties in the list or an admin
        /// </summary>
        /// <param name="propertyIds">The list of property IDs to check</param>
        /// <param name="userId">The ID of the current user</param>
        /// <returns>True if the user is the owner of all properties or an admin, false otherwise</returns>
        private async Task<bool> IsUserAuthorizedForAllProperties(List<Guid> propertyIds, Guid userId)
        {
            // Check if user is admin (you may need to implement this based on your authorization system)
            bool isAdmin = User.IsInRole("Admin");
            if (isAdmin)
            {
                return true;
            }

            // Check if user is the owner of all properties
            foreach (var propertyId in propertyIds)
            {
                var isAuthorized = await IsUserAuthorizedForProperty(propertyId, userId);
                if (!isAuthorized)
                {
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// Updates existing media files for a property with new Caption and IsAvatar values
        /// </summary>
        /// <param name="propertyId">The ID of the property</param>
        /// <param name="propertyMediaDtos">List of property media DTOs with updated information</param>
        /// <returns>A task representing the asynchronous operation</returns>
        private async Task UpdateExistingMediaFiles(Guid propertyId, List<PropertyMediaDto> propertyMediaDtos)
        {
            // Get existing media files for the property
            var existingMedia = await _mediaServices.GetMediaByPropertyIdAsync(propertyId);

            // Handle IsAvatar uniqueness - ensure only one image can be avatar
            var avatarMedia = propertyMediaDtos.FirstOrDefault(x => x.IsAvatar);
            if (avatarMedia != null)
            {
                // Reset any existing avatar for this property that's not the new avatar
                foreach (var existingMediaItem in existingMedia)
                {
                    if (existingMediaItem.IsAvatar && existingMediaItem.Id != avatarMedia.Id)
                    {
                        // Reset existing avatar
                        var resetMediaDto = new PropertyMediaDto
                        {
                            Id = existingMediaItem.Id,
                            PropertyID = existingMediaItem.PropertyID,
                            MediaType = existingMediaItem.MediaType,
                            MediaURL = existingMediaItem.MediaURL,
                            FilePath = existingMediaItem.FilePath,
                            UploadedAt = existingMediaItem.UploadedAt,
                            Caption = existingMediaItem.Caption,
                            IsAvatar = false
                        };
                        await _mediaServices.UpdateMediaAsync(existingMediaItem.Id, propertyId, existingMediaItem.FilePath ?? string.Empty, resetMediaDto);
                    }
                }
            }

            // Update each media file with new Caption and IsAvatar values
            foreach (var mediaDto in propertyMediaDtos)
            {
                var existingMediaItem = existingMedia.FirstOrDefault(x => x.Id == mediaDto.Id);
                if (existingMediaItem != null)
                {
                    // Update the existing media with new Caption and IsAvatar values
                    var updatedMediaDto = new PropertyMediaDto
                    {
                        Id = existingMediaItem.Id,
                        PropertyID = existingMediaItem.PropertyID,
                        MediaType = existingMediaItem.MediaType,
                        MediaURL = existingMediaItem.MediaURL,
                        FilePath = existingMediaItem.FilePath,
                        UploadedAt = existingMediaItem.UploadedAt,
                        Caption = mediaDto.Caption, // Update from DTO
                        IsAvatar = mediaDto.IsAvatar // Update from DTO
                    };
                    await _mediaServices.UpdateMediaAsync(existingMediaItem.Id, propertyId, existingMediaItem.FilePath ?? string.Empty, updatedMediaDto);
                }
            }
        }

        /// <summary>
        /// Moves temporary images to the property folder and updates their database records
        /// </summary>
        /// <param name="propertyId">The ID of the property</param>
        /// <param name="imageIds">Optional list of image IDs to move</param>
        /// <param name="propertyMediaDtos">Optional list of property media DTOs</param>
        /// <returns>A task representing the asynchronous operation</returns>
        private async Task MoveTempImagesToPropertyFolder(Guid propertyId, List<Guid>? imageIds = null, List<PropertyMediaDto>? propertyMediaDtos = null)
        {
            string tempFolder = Path.Combine("Temp", DateTime.Today.ToString("yyyyMMdd"));
            string propertyFolder = Path.Combine("PropertyImages", propertyId.ToString());

            if (!Directory.Exists(propertyFolder))
            {
                Directory.CreateDirectory(propertyFolder);
            }

            // Lấy danh sách ảnh chưa có propertyId nhưng được gửi lên lúc insert/update
            if (imageIds != null)
            {
                var tempImages = await _mediaServices.GetAllMediaAsync(imageIds);

                // Handle IsAvatar uniqueness - ensure only one image can be avatar
                var avatarMedia = propertyMediaDtos?.FirstOrDefault(x => x.IsAvatar);
                if (avatarMedia != null)
                {
                    // Reset any existing avatar for this property
                    var existingPropertyMedia = await _mediaServices.GetMediaByPropertyIdAsync(propertyId);
                    foreach (var existingMedia in existingPropertyMedia)
                    {
                        if (existingMedia.IsAvatar && existingMedia.Id != avatarMedia.Id)
                        {
                            // Reset existing avatar
                            var resetMediaDto = new PropertyMediaDto
                            {
                                Id = existingMedia.Id,
                                PropertyID = existingMedia.PropertyID,
                                MediaType = existingMedia.MediaType,
                                MediaURL = existingMedia.MediaURL,
                                FilePath = existingMedia.FilePath,
                                UploadedAt = existingMedia.UploadedAt,
                                Caption = existingMedia.Caption,
                                IsAvatar = false
                            };
                            await _mediaServices.UpdateMediaAsync(existingMedia.Id, propertyId, existingMedia.FilePath ?? string.Empty, resetMediaDto);
                        }
                    }
                }

                foreach (var media in tempImages)
                {
                    var tempMedia = propertyMediaDtos?.FirstOrDefault(x => x.Id == media.Id);
                    string tempFilePath = media.FilePath ?? string.Empty;
                    string newFilePath = Path.Combine(propertyFolder, media.Id.ToString() + Path.GetExtension(media.FilePath));

                    if (System.IO.File.Exists(tempFilePath))
                    {
                        // Di chuyển file
                        System.IO.File.Move(tempFilePath, newFilePath);

                        // Cập nhật database với Caption và IsAvatar từ DTO
                        if (tempMedia != null)
                        {
                            await _mediaServices.UpdateMediaAsync(media.Id, propertyId, newFilePath, tempMedia);
                        }
                        else
                        {
                            // Nếu không có thông tin từ DTO, chỉ cập nhật đường dẫn file
                            var defaultMediaDto = new PropertyMediaDto
                            {
                                Id = media.Id,
                                PropertyID = propertyId,
                                MediaType = media.MediaType,
                                MediaURL = media.MediaURL,
                                FilePath = newFilePath,
                                UploadedAt = media.UploadedAt,
                                Caption = media.Caption,
                                IsAvatar = media.IsAvatar
                            };
                            await _mediaServices.UpdateMediaAsync(media.Id, propertyId, newFilePath, defaultMediaDto);
                        }
                    }
                }
            }


        }

    }
}
