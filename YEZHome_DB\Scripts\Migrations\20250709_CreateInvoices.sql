CREATE TABLE "Invoices" (
    "Id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "UserId" UUID NOT NULL,
    "PropertyId" UUID NOT NULL,
    "Type" VARCHAR(50) NOT NULL, -- property spending type (new_post, renew, highlight )
    "TotalAmount" INT NOT NULL CHECK ("TotalAmount" >= 0),
    "Status" VARCHAR(20) NOT NULL DEFAULT 'PENDING', -- COMPLETED, PENDING, FAILED, CANCELLED
    "Note" TEXT,
    "CreatedAt" TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    "PaidAt" TIMESTAMPTZ
);

CREATE TABLE "InvoiceItems" (
    "Id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "InvoiceId" UUID NOT NULL REFERENCES "Invoices"("Id") ON DELETE CASCADE,
    "ItemType" VARCHAR(50) NOT NULL, -- property spending type (new_post, renew, highlight)
    "Description" TEXT,
    "Amount" INT NOT NULL CHECK ("Amount" >= 0)
);
