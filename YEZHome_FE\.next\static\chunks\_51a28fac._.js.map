{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/dialog.jsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { Dialog as DialogPrimitive } from \"radix-ui\"\r\nimport { X } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Dialog = DialogPrimitive.Root\r\n\r\nconst DialogTrigger = DialogPrimitive.Trigger\r\n\r\nconst DialogPortal = DialogPrimitive.Portal\r\n\r\nconst DialogClose = DialogPrimitive.Close\r\n\r\nconst DialogOverlay = React.forwardRef(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Overlay\r\n    ref={ref}\r\n    className={cn(\r\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\r\n      className\r\n    )}\r\n    {...props} />\r\n))\r\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\r\n\r\nconst DialogContent = React.forwardRef(({ className, children, ...props }, ref) => (\r\n  <DialogPortal>\r\n    <DialogOverlay />\r\n    <DialogPrimitive.Content\r\n      ref={ref}\r\n      className={cn(\r\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\r\n        className\r\n      )}\r\n      {...props}>\r\n      {children}\r\n      <DialogPrimitive.Close\r\n        className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\r\n        <X className=\"h-4 w-4\" />\r\n        <span className=\"sr-only\">Close</span>\r\n      </DialogPrimitive.Close>\r\n    </DialogPrimitive.Content>\r\n  </DialogPortal>\r\n))\r\nDialogContent.displayName = DialogPrimitive.Content.displayName\r\n\r\nconst DialogHeader = ({\r\n  className,\r\n  ...props\r\n}) => (\r\n  <div\r\n    className={cn(\"flex flex-col space-y-1.5 text-center sm:text-left\", className)}\r\n    {...props} />\r\n)\r\nDialogHeader.displayName = \"DialogHeader\"\r\n\r\nconst DialogFooter = ({\r\n  className,\r\n  ...props\r\n}) => (\r\n  <div\r\n    className={cn(\"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\", className)}\r\n    {...props} />\r\n)\r\nDialogFooter.displayName = \"DialogFooter\"\r\n\r\nconst DialogTitle = React.forwardRef(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Title\r\n    ref={ref}\r\n    className={cn(\"text-lg font-semibold leading-none tracking-tight\", className)}\r\n    {...props} />\r\n))\r\nDialogTitle.displayName = DialogPrimitive.Title.displayName\r\n\r\nconst DialogDescription = React.forwardRef(({ className, ...props }, ref) => (\r\n  <DialogPrimitive.Description\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props} />\r\n))\r\nDialogDescription.displayName = DialogPrimitive.Description.displayName\r\n\r\nexport {\r\n  Dialog,\r\n  DialogPortal,\r\n  DialogOverlay,\r\n  DialogTrigger,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogFooter,\r\n  DialogTitle,\r\n  DialogDescription,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,uMAAA,CAAA,SAAe,CAAC,IAAI;AAEnC,MAAM,gBAAgB,uMAAA,CAAA,SAAe,CAAC,OAAO;AAE7C,MAAM,eAAe,uMAAA,CAAA,SAAe,CAAC,MAAM;AAE3C,MAAM,cAAc,uMAAA,CAAA,SAAe,CAAC,KAAK;AAEzC,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC/D,6LAAC,uMAAA,CAAA,SAAe,CAAC,OAAO;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;;;;;;KAPP;AASN,cAAc,WAAW,GAAG,uMAAA,CAAA,SAAe,CAAC,OAAO,CAAC,WAAW;AAE/D,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzE,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,uMAAA,CAAA,SAAe,CAAC,OAAO;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBACR;kCACD,6LAAC,uMAAA,CAAA,SAAe,CAAC,KAAK;wBACpB,WAAU;;0CACV,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,uMAAA,CAAA,SAAe,CAAC,OAAO,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACJ,iBACC,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,sDAAsD;QACnE,GAAG,KAAK;;;;;;MANP;AAQN,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACJ,iBACC,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iEAAiE;QAC9E,GAAG,KAAK;;;;;;MANP;AAQN,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC7D,6LAAC,uMAAA,CAAA,SAAe,CAAC,KAAK;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,qDAAqD;QAClE,GAAG,KAAK;;;;;;;AAEb,YAAY,WAAW,GAAG,uMAAA,CAAA,SAAe,CAAC,KAAK,CAAC,WAAW;AAE3D,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACnE,6LAAC,uMAAA,CAAA,SAAe,CAAC,WAAW;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAEb,kBAAkB,WAAW,GAAG,uMAAA,CAAA,SAAe,CAAC,WAAW,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/scroll-area.jsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { ScrollArea as ScrollAreaPrimitive } from \"radix-ui\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst ScrollArea = React.forwardRef(({ className, children, ...props }, ref) => (\r\n  <ScrollAreaPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\"relative overflow-hidden\", className)}\r\n    {...props}>\r\n    <ScrollAreaPrimitive.Viewport className=\"h-full w-full rounded-[inherit]\">\r\n      {children}\r\n    </ScrollAreaPrimitive.Viewport>\r\n    <ScrollBar />\r\n    <ScrollAreaPrimitive.Corner />\r\n  </ScrollAreaPrimitive.Root>\r\n))\r\nScrollArea.displayName = ScrollAreaPrimitive.Root.displayName\r\n\r\nconst ScrollBar = React.forwardRef(({ className, orientation = \"vertical\", ...props }, ref) => (\r\n  <ScrollAreaPrimitive.ScrollAreaScrollbar\r\n    ref={ref}\r\n    orientation={orientation}\r\n    className={cn(\r\n      \"flex touch-none select-none transition-colors\",\r\n      orientation === \"vertical\" &&\r\n        \"h-full w-2.5 border-l border-l-transparent p-px\",\r\n      orientation === \"horizontal\" &&\r\n        \"h-2.5 flex-col border-t border-t-transparent p-px\",\r\n      className\r\n    )}\r\n    {...props}>\r\n    <ScrollAreaPrimitive.ScrollAreaThumb className=\"relative flex-1 rounded-full bg-border\" />\r\n  </ScrollAreaPrimitive.ScrollAreaScrollbar>\r\n))\r\nScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName\r\n\r\nexport { ScrollArea, ScrollBar }\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAAE,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACtE,6LAAC,mNAAA,CAAA,aAAmB,CAAC,IAAI;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;0BACT,6LAAC,mNAAA,CAAA,aAAmB,CAAC,QAAQ;gBAAC,WAAU;0BACrC;;;;;;0BAEH,6LAAC;;;;;0BACD,6LAAC,mNAAA,CAAA,aAAmB,CAAC,MAAM;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,mNAAA,CAAA,aAAmB,CAAC,IAAI,CAAC,WAAW;AAE7D,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,cAAc,UAAU,EAAE,GAAG,OAAO,EAAE,oBACrF,6LAAC,mNAAA,CAAA,aAAmB,CAAC,mBAAmB;QACtC,KAAK;QACL,aAAa;QACb,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,iDACA,gBAAgB,cACd,mDACF,gBAAgB,gBACd,qDACF;QAED,GAAG,KAAK;kBACT,cAAA,6LAAC,mNAAA,CAAA,aAAmB,CAAC,eAAe;YAAC,WAAU;;;;;;;;;;;MAb7C;AAgBN,UAAU,WAAW,GAAG,mNAAA,CAAA,aAAmB,CAAC,mBAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 235, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/actions/server/userFavorite.jsx"], "sourcesContent": ["\"use server\";\r\n\r\nimport { handleErrorResponse, logError } from \"@/lib/apiUtils\";\r\nimport { fetchWithAuth } from \"@/lib/sessionUtils\";\r\n\r\nconst API_BASE_URL = `${process.env.API_URL}/api/UserFavorites`;\r\n\r\nexport async function addToFavorites(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/add`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({ propertyId }),\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"addToFavorites\",\r\n      propertyId,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi thêm vào danh sách yêu thích\");\r\n  }\r\n}\r\n\r\nexport async function removeFromFavorites(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/remove/${propertyId}`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"removeFromFavorites\",\r\n      propertyId,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi xóa khỏi danh sách yêu thích\");\r\n  }\r\n}\r\n\r\nexport async function checkFavoriteStatus(propertyIds) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/check`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({ propertyIds: Array.isArray(propertyIds) ? propertyIds : [propertyIds] }),\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"checkFavoriteStatus\",\r\n      propertyIds,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi kiểm tra trạng thái yêu thích\");\r\n  }\r\n}\r\n\r\nexport async function getFavoritesCount() {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/count`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getFavoritesCount\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy số lượng bất động sản yêu thích\");\r\n  }\r\n}\r\n\r\n/**\r\n * Gets the user's favorite properties\r\n * @returns {Promise<{success: boolean, data: Array<UserFavoriteDto>, message: string}>} Response with array of UserFavoriteDto objects\r\n * @description UserFavoriteDto contains: id, propertyId, createdAt\r\n */\r\nexport async function getUserFavorites() {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/favorites`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getUserFavorites\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy danh sách bất động sản yêu thích\");\r\n  }\r\n}\r\n\r\n/**\r\n * Gets the user's favorite properties with full property details, pagination and filtering\r\n * @param {Object} filters - Filter options\r\n * @param {number} filters.minPrice - Minimum price filter\r\n * @param {number} filters.maxPrice - Maximum price filter\r\n * @param {string} filters.fromDate - Start date filter (ISO string)\r\n * @param {string} filters.toDate - End date filter (ISO string)\r\n * @param {string} filters.sortBy - Sort field (CreatedAt, Price)\r\n * @param {boolean} filters.sortDescending - Sort direction\r\n * @param {number} filters.page - Page number\r\n * @param {number} filters.pageSize - Items per page\r\n * @returns {Promise<{success: boolean, data: PagedFavoriteResultDto, message: string}>} Response with paginated favorites and property details\r\n */\r\nexport async function getUserFavoritesWithDetails(filters = {}) {\r\n  try {\r\n    const queryParams = new URLSearchParams();\r\n\r\n    if (filters.minPrice !== undefined && filters.minPrice !== null) {\r\n      queryParams.append('minPrice', filters.minPrice.toString());\r\n    }\r\n    if (filters.maxPrice !== undefined && filters.maxPrice !== null) {\r\n      queryParams.append('maxPrice', filters.maxPrice.toString());\r\n    }\r\n    if (filters.fromDate) {\r\n      queryParams.append('fromDate', filters.fromDate);\r\n    }\r\n    if (filters.toDate) {\r\n      queryParams.append('toDate', filters.toDate);\r\n    }\r\n    if (filters.sortBy) {\r\n      queryParams.append('sortBy', filters.sortBy);\r\n    }\r\n    if (filters.sortDescending !== undefined) {\r\n      queryParams.append('sortDescending', filters.sortDescending.toString());\r\n    }\r\n    if (filters.page) {\r\n      queryParams.append('page', filters.page.toString());\r\n    }\r\n    if (filters.pageSize) {\r\n      queryParams.append('pageSize', filters.pageSize.toString());\r\n    }\r\n\r\n    const url = `${API_BASE_URL}/favorites-with-details${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;\r\n\r\n    return await fetchWithAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getUserFavoritesWithDetails\",\r\n      filters,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy danh sách bất động sản yêu thích\");\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAOsB,iBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 251, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/actions/server/userFavorite.jsx"], "sourcesContent": ["\"use server\";\r\n\r\nimport { handleErrorResponse, logError } from \"@/lib/apiUtils\";\r\nimport { fetchWithAuth } from \"@/lib/sessionUtils\";\r\n\r\nconst API_BASE_URL = `${process.env.API_URL}/api/UserFavorites`;\r\n\r\nexport async function addToFavorites(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/add`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({ propertyId }),\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"addToFavorites\",\r\n      propertyId,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi thêm vào danh sách yêu thích\");\r\n  }\r\n}\r\n\r\nexport async function removeFromFavorites(propertyId) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/remove/${propertyId}`, {\r\n      method: \"DELETE\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"removeFromFavorites\",\r\n      propertyId,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi xóa khỏi danh sách yêu thích\");\r\n  }\r\n}\r\n\r\nexport async function checkFavoriteStatus(propertyIds) {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/check`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({ propertyIds: Array.isArray(propertyIds) ? propertyIds : [propertyIds] }),\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"checkFavoriteStatus\",\r\n      propertyIds,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi kiểm tra trạng thái yêu thích\");\r\n  }\r\n}\r\n\r\nexport async function getFavoritesCount() {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/count`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getFavoritesCount\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy số lượng bất động sản yêu thích\");\r\n  }\r\n}\r\n\r\n/**\r\n * Gets the user's favorite properties\r\n * @returns {Promise<{success: boolean, data: Array<UserFavoriteDto>, message: string}>} Response with array of UserFavoriteDto objects\r\n * @description UserFavoriteDto contains: id, propertyId, createdAt\r\n */\r\nexport async function getUserFavorites() {\r\n  try {\r\n    return await fetchWithAuth(`${API_BASE_URL}/favorites`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getUserFavorites\",\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy danh sách bất động sản yêu thích\");\r\n  }\r\n}\r\n\r\n/**\r\n * Gets the user's favorite properties with full property details, pagination and filtering\r\n * @param {Object} filters - Filter options\r\n * @param {number} filters.minPrice - Minimum price filter\r\n * @param {number} filters.maxPrice - Maximum price filter\r\n * @param {string} filters.fromDate - Start date filter (ISO string)\r\n * @param {string} filters.toDate - End date filter (ISO string)\r\n * @param {string} filters.sortBy - Sort field (CreatedAt, Price)\r\n * @param {boolean} filters.sortDescending - Sort direction\r\n * @param {number} filters.page - Page number\r\n * @param {number} filters.pageSize - Items per page\r\n * @returns {Promise<{success: boolean, data: PagedFavoriteResultDto, message: string}>} Response with paginated favorites and property details\r\n */\r\nexport async function getUserFavoritesWithDetails(filters = {}) {\r\n  try {\r\n    const queryParams = new URLSearchParams();\r\n\r\n    if (filters.minPrice !== undefined && filters.minPrice !== null) {\r\n      queryParams.append('minPrice', filters.minPrice.toString());\r\n    }\r\n    if (filters.maxPrice !== undefined && filters.maxPrice !== null) {\r\n      queryParams.append('maxPrice', filters.maxPrice.toString());\r\n    }\r\n    if (filters.fromDate) {\r\n      queryParams.append('fromDate', filters.fromDate);\r\n    }\r\n    if (filters.toDate) {\r\n      queryParams.append('toDate', filters.toDate);\r\n    }\r\n    if (filters.sortBy) {\r\n      queryParams.append('sortBy', filters.sortBy);\r\n    }\r\n    if (filters.sortDescending !== undefined) {\r\n      queryParams.append('sortDescending', filters.sortDescending.toString());\r\n    }\r\n    if (filters.page) {\r\n      queryParams.append('page', filters.page.toString());\r\n    }\r\n    if (filters.pageSize) {\r\n      queryParams.append('pageSize', filters.pageSize.toString());\r\n    }\r\n\r\n    const url = `${API_BASE_URL}/favorites-with-details${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;\r\n\r\n    return await fetchWithAuth(url, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n  } catch (error) {\r\n    logError(\"UserService\", error, {\r\n      action: \"getUserFavoritesWithDetails\",\r\n      filters,\r\n    });\r\n    return handleErrorResponse(false, null, \"Đã xảy ra lỗi khi lấy danh sách bất động sản yêu thích\");\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAyBsB,sBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 267, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/ButtonLoading.jsx"], "sourcesContent": ["import { RotateCw } from \"lucide-react\";\r\nimport { useTranslations } from \"next-intl\";\r\n\r\nexport default function ButtonLoading({ ...props }) {\r\n  const { showLoading, type, title } = props;\r\n  const t = useTranslations(\"Common\");\r\n  return (\r\n    <button\r\n      type={type}\r\n      disabled={showLoading}\r\n      className=\"\r\n        group relative w-full flex items-center justify-center\r\n        py-2.5 px-4 \r\n        border border-transparent\r\n        text-lg font-semibold\r\n        rounded-md text-white\r\n        bg-teal-600 hover:bg-teal-700\r\n        focus:outline-hidden focus:ring-2 focus:ring-teal-500 focus:ring-offset-2\r\n        transition-all duration-200 ease-in-out\r\n        disabled:opacity-60 disabled:cursor-not-allowed\"\r\n    >\r\n      {showLoading ? (\r\n        <>\r\n          <RotateCw className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" />\r\n          {t(\"loading\")}\r\n        </>\r\n      ) : (\r\n        <>{title}</>\r\n      )}\r\n    </button>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;;AAEe,SAAS,cAAc,EAAE,GAAG,OAAO;;IAChD,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG;IACrC,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,qBACE,6LAAC;QACC,MAAM;QACN,UAAU;QACV,WAAU;kBAWT,4BACC;;8BACE,6LAAC,iNAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;gBACnB,EAAE;;yCAGL;sBAAG;;;;;;;AAIX;GA5BwB;;QAEZ,yMAAA,CAAA,kBAAe;;;KAFH", "debugId": null}}, {"offset": {"line": 322, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/actions/server/authenticate.jsx"], "sourcesContent": ["\"use server\";\r\n\r\nimport { handleErrorResponse } from \"@/lib/apiUtils\";\r\nimport { changePasswordSchema, forgetPasswordSchema, loginSchema, registerSchema } from \"@/lib/schemas/authSchema\";\r\nimport { createSession, deleteSession, fetchWithAuth, getJwtInfo, getSession, verifyJwtToken } from \"@/lib/sessionUtils\";\r\nimport { redirect } from \"next/navigation\";\r\nimport { revalidatePath } from \"next/cache\";\r\n\r\nexport async function registerUser(prevState, formData) {\r\n  const formDataObject = Object.fromEntries(formData.entries());\r\n\r\n  const validatedFields = registerSchema.safeParse(formDataObject);\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      message: \"Dữ liệu không hợp lệ. Vui lòng kiểm tra lại thông tin.\",\r\n      fieldValues: formDataObject,\r\n    };\r\n  }\r\n\r\n  try {\r\n    const response = await fetch(`${process.env.API_URL}/api/Auth/register`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify(validatedFields.data),\r\n    });\r\n\r\n    if (!response.ok) {\r\n      console.error(\"Registration failed:\", response);\r\n      const errorData = await response.json().catch(() => null);\r\n      return handleErrorResponse(false, formDataObject, errorData?.message || \"Registration failed. Please try again.\");\r\n    }\r\n  } catch (error) {\r\n    return handleErrorResponse(false, formDataObject, \"Failed to connect to the server. Please try again later.\");\r\n  }\r\n\r\n  redirect(\"/dang-ki/dang-ki-thanh-cong\");\r\n}\r\n\r\nexport async function loginUser(prevState, formData) {\r\n  const validatedFields = loginSchema.safeParse({\r\n    email: formData.get(\"email\"),\r\n    password: formData.get(\"password\"),\r\n  });\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      message: \"Dữ liệu không hợp lệ. Vui lòng kiểm tra lại thông tin.\",\r\n      fieldValues: {\r\n        email: formData.get(\"email\"),\r\n      },\r\n    };\r\n  }\r\n\r\n  let urlCallback = \"/user/profile\";\r\n\r\n  try {\r\n    const response = await fetch(`${process.env.API_URL}/api/Auth/login`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        email: validatedFields.data.email,\r\n        password: validatedFields.data.password,\r\n      }),\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorData = await response.json().catch(() => null);\r\n      return handleErrorResponse(false, { email: formData.get(\"email\") }, errorData?.message || \"Thông tin đăng nhập không đúng\");\r\n    }\r\n\r\n    const data = await response.json();\r\n    const token = data.token;\r\n    const user = {\r\n      id: data.id,\r\n      fullName: data.fullName,\r\n      email: data.email,\r\n      userType: data.userType,\r\n      phone: data.phone,\r\n      lastLogin: data.lastLogin,\r\n    };\r\n\r\n    await createSession(\"Authorization\", token);\r\n    await createSession(\"UserProfile\", JSON.stringify(user));\r\n  } catch (error) {\r\n    return handleErrorResponse(false, { email: formData.get(\"email\") }, \"Failed to connect to the server. Please try again later.\");\r\n  }\r\n  revalidatePath('/');\r\n\r\n  await new Promise(resolve => setTimeout(resolve, 100));\r\n\r\n  redirect(urlCallback);\r\n}\r\n\r\nexport async function changePassword(prevState, formData) {\r\n  const formDataObject = Object.fromEntries(formData.entries());\r\n\r\n  const validatedFields = changePasswordSchema.safeParse(formDataObject);\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      message: \"Dữ liệu không hợp lệ. Vui lòng kiểm tra lại thông tin.\",\r\n      fieldValues: formDataObject,\r\n    };\r\n  }\r\n\r\n  const jwtData = await getJwtInfo();\r\n\r\n  let payload = {\r\n    email: jwtData.email,\r\n    oldPassword: validatedFields.data.oldPassword,\r\n    newPassword: validatedFields.data.newPassword,\r\n  };\r\n\r\n  return await fetchWithAuth(`${process.env.API_URL}/api/Auth/me/password`, {\r\n    method: \"PATCH\",\r\n    body: JSON.stringify(payload),\r\n    headers: {\r\n      \"Content-Type\": \"application/json\",\r\n    },\r\n  });\r\n}\r\n\r\nexport async function forgotPassword(prevState, formData) {\r\n  const formDataObject = Object.fromEntries(formData.entries());\r\n\r\n  const validatedFields = forgetPasswordSchema.safeParse(formDataObject);\r\n\r\n  if (!validatedFields.success) {\r\n    return {\r\n      errors: validatedFields.error.flatten().fieldErrors,\r\n      message: \"Dữ liệu không hợp lệ. Vui lòng kiểm tra lại thông tin.\",\r\n      fieldValues: formDataObject,\r\n    };\r\n  }\r\n\r\n  try {\r\n    const response = await fetch(`${process.env.API_URL}/api/Auth/reset-password`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        email: validatedFields.data.email,\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    return handleErrorResponse(false, { email: formData.get(\"email\") }, \"Failed to connect to the server. Please try again later.\");\r\n  }\r\n\r\n  redirect(\"/da-gui-email-khoi-phuc-mat-khau\");\r\n}\r\n\r\nexport async function getUserProfile() {\r\n  return await fetchWithAuth(`${process.env.API_URL}/api/Auth/me`);\r\n}\r\n\r\nexport async function validateTokenDirectlyFromAPIServer() {\r\n  return await fetchWithAuth(`${process.env.API_URL}/api/Auth/validate-token`);\r\n}\r\n\r\nexport async function validateTokenServer() {\r\n  const token = await getSession(\"Authorization\");\r\n  if (!token) {\r\n    return { isLoggedIn: false, isExpired: false, errorType: 'no_token' };\r\n  }\r\n\r\n  const decoded = await verifyJwtToken(token);\r\n  if (!decoded) {\r\n    deleteSession(\"Authorization\");\r\n    deleteSession(\"UserProfile\");\r\n    return { isLoggedIn: false, isExpired: true};\r\n  }\r\n\r\n  return { isLoggedIn: true, isExpired: false };\r\n}\r\n\r\nexport async function logout() {\r\n  await deleteSession(\"Authorization\");\r\n  await deleteSession(\"UserProfile\");\r\n  redirect(\"/\");\r\n}\r\n"], "names": [], "mappings": ";;;;;;IA0CsB,YAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 338, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/alert.jsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst alertVariants = cva(\r\n  \"relative w-full rounded-lg border px-4 py-3 text-sm [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg~*]:pl-7\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-background text-foreground\",\r\n        destructive:\r\n          \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nconst Alert = React.forwardRef(({ className, variant, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    role=\"alert\"\r\n    className={cn(alertVariants({ variant }), className)}\r\n    {...props} />\r\n))\r\nAlert.displayName = \"Alert\"\r\n\r\nconst AlertTitle = React.forwardRef(({ className, ...props }, ref) => (\r\n  <h5\r\n    ref={ref}\r\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\r\n    {...props} />\r\n))\r\nAlertTitle.displayName = \"AlertTitle\"\r\n\r\nconst AlertDescription = React.forwardRef(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm \", className)}\r\n    {...props} />\r\n))\r\nAlertDescription.displayName = \"AlertDescription\"\r\n\r\nexport { Alert, AlertTitle, AlertDescription }\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,mJACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAAE,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAChE,6LAAC;QACC,KAAK;QACL,MAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;;AAEb,MAAM,WAAW,GAAG;AAEpB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC5D,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;;AAEb,WAAW,WAAW,GAAG;AAEzB,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAClE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;;;;;;AAEb,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 415, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/ui/input.jsx"], "sourcesContent": ["import * as React from \"react\";\r\n\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nconst Input = React.forwardRef(({ className, type, suffix, ...props }, ref) => {\r\n  return (\r\n    <div className=\"relative flex items-center\">\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-xs transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-hidden focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n          suffix ? \"pr-10\" : \"\", // Adjust padding if suffix exists\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n      {suffix && <span className=\"absolute right-3 text-muted-foreground text-sm\">{suffix}</span>}\r\n    </div>\r\n  );\r\n});\r\nInput.displayName = \"Input\";\r\n\r\nexport { Input };\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAAE,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,EAAE;IACrE,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,MAAM;gBACN,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,6WACA,SAAS,UAAU,IACnB;gBAEF,KAAK;gBACJ,GAAG,KAAK;;;;;;YAEV,wBAAU,6LAAC;gBAAK,WAAU;0BAAkD;;;;;;;;;;;;AAGnF;;AACA,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 468, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/auth/LoginForm.jsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { Eye, EyeOff, CircleAlert, Mail, Lock } from \"lucide-react\";\nimport ButtonLoading from \"@/components/ui/ButtonLoading\";\nimport { useActionState } from \"react\";\nimport { loginUser } from \"@/app/actions/server/authenticate\";\nimport { Alert, AlertDescription, AlertTitle } from \"@/components/ui/alert\";\nimport { Link } from \"@/i18n/navigation\";\nimport { useTranslations } from \"next-intl\";\nimport { Input } from \"../ui/input\";\n\nconst initialState = {\n  errors: {},\n  message: null,\n  fieldValues: {\n    email: \"\",\n    password: \"\",\n  },\n};\n\nexport default function LoginForm() {\n  const t = useTranslations(\"LoginPage\");\n  const [showPassword, setShowPassword] = useState(false);\n  const [state, formAction, isPending] = useActionState(loginUser, initialState);\n\n  return (\n    <form className=\"space-y-6\" action={formAction}>\n      {state?.message && (\n        <Alert variant=\"destructive\" className=\"bg-red-600 text-white border-red-800\">\n          <CircleAlert className=\"h-4 w-4 text-white\" />\n          <AlertTitle className=\"text-white\">{t(\"loginErrorTitle\")}</AlertTitle>\n          <AlertDescription className=\"text-white\">{state.message}</AlertDescription>\n        </Alert>\n      )}\n      <div className=\"space-y-2\">\n        <label htmlFor=\"email\" className=\"sr-only\">\n          {t(\"emailLabel\")}\n        </label>\n        <div className=\"relative\">\n          <Mail className=\"absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400\" />\n          <Input\n            id=\"email\"\n            name=\"email\"\n            type=\"email\"\n            autoComplete=\"email\"\n            required\n            defaultValue={state.fieldValues?.email}\n            className=\"pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-teal-500 focus:border-teal-500\"\n            placeholder={t(\"emailPlaceholder\")}\n          />\n          {state.errors?.email && <p className=\"mt-1 text-xs text-red-500\">{state.errors.email[0]}</p>}\n        </div>\n      </div>\n      <div className=\"space-y-2\">\n        <label htmlFor=\"password\" className=\"sr-only\">\n          {t(\"passwordLabel\")}\n        </label>\n        <div className=\"relative\">\n          <Lock className=\"absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-400\" />\n          <Input\n            id=\"password\"\n            name=\"password\"\n            type={showPassword ? \"text\" : \"password\"}\n            autoComplete=\"current-password\"\n            required\n            defaultValue={state.fieldValues?.password}\n            className=\"pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-teal-500 focus:border-teal-500\"\n            placeholder={t(\"passwordPlaceholder\")}\n          />\n          <button\n            type=\"button\"\n            className=\"absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600\"\n            onClick={() => setShowPassword(!showPassword)}\n          >\n            {showPassword ? <EyeOff className=\"h-5 w-5 text-gray-400\" /> : <Eye className=\"h-5 w-5 text-gray-400\" />}\n          </button>\n          {state.errors?.password && <p className=\"mt-1 text-xs text-red-500\">{state.errors.password[0]}</p>}\n        </div>\n      </div>\n\n      <div className=\"flex items-center justify-between text-sm\">\n        <div className=\"flex items-center\">\n          <input id=\"remember-me\" name=\"remember-me\" type=\"checkbox\" className=\"h-4 w-4\" />\n          <label htmlFor=\"remember-me\" className=\"ml-2 block text-sm\">\n            {t(\"rememberMeLabel\")}\n          </label>\n        </div>\n\n        <div className=\"text-sm\">\n          <Link href=\"/quen-mat-khau\" className=\"text-teal-600 hover:underline\">\n            {t(\"forgotPasswordLink\")}\n          </Link>\n        </div>\n      </div>\n\n      <div>\n        <ButtonLoading type=\"submit\" showLoading={isPending} title={t(\"loginButton\")} />\n      </div>\n    </form>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;;AAYA,MAAM,eAAe;IACnB,QAAQ,CAAC;IACT,SAAS;IACT,aAAa;QACX,OAAO;QACP,UAAU;IACZ;AACF;AAEe,SAAS;;IACtB,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,YAAY,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,iBAAc,AAAD,EAAE,mKAAA,CAAA,YAAS,EAAE;IAEjE,qBACE,6LAAC;QAAK,WAAU;QAAY,QAAQ;;YACjC,OAAO,yBACN,6LAAC,6HAAA,CAAA,QAAK;gBAAC,SAAQ;gBAAc,WAAU;;kCACrC,6LAAC,uNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,6LAAC,6HAAA,CAAA,aAAU;wBAAC,WAAU;kCAAc,EAAE;;;;;;kCACtC,6LAAC,6HAAA,CAAA,mBAAgB;wBAAC,WAAU;kCAAc,MAAM,OAAO;;;;;;;;;;;;0BAG3D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAM,SAAQ;wBAAQ,WAAU;kCAC9B,EAAE;;;;;;kCAEL,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6LAAC,6HAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,MAAK;gCACL,MAAK;gCACL,cAAa;gCACb,QAAQ;gCACR,cAAc,MAAM,WAAW,EAAE;gCACjC,WAAU;gCACV,aAAa,EAAE;;;;;;4BAEhB,MAAM,MAAM,EAAE,uBAAS,6LAAC;gCAAE,WAAU;0CAA6B,MAAM,MAAM,CAAC,KAAK,CAAC,EAAE;;;;;;;;;;;;;;;;;;0BAG3F,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAM,SAAQ;wBAAW,WAAU;kCACjC,EAAE;;;;;;kCAEL,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6LAAC,6HAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,MAAK;gCACL,MAAM,eAAe,SAAS;gCAC9B,cAAa;gCACb,QAAQ;gCACR,cAAc,MAAM,WAAW,EAAE;gCACjC,WAAU;gCACV,aAAa,EAAE;;;;;;0CAEjB,6LAAC;gCACC,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,gBAAgB,CAAC;0CAE/B,6BAAe,6LAAC,6MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;yDAA6B,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;;;;;;4BAE/E,MAAM,MAAM,EAAE,0BAAY,6LAAC;gCAAE,WAAU;0CAA6B,MAAM,MAAM,CAAC,QAAQ,CAAC,EAAE;;;;;;;;;;;;;;;;;;0BAIjG,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,IAAG;gCAAc,MAAK;gCAAc,MAAK;gCAAW,WAAU;;;;;;0CACrE,6LAAC;gCAAM,SAAQ;gCAAc,WAAU;0CACpC,EAAE;;;;;;;;;;;;kCAIP,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,qHAAA,CAAA,OAAI;4BAAC,MAAK;4BAAiB,WAAU;sCACnC,EAAE;;;;;;;;;;;;;;;;;0BAKT,6LAAC;0BACC,cAAA,6LAAC,qIAAA,CAAA,UAAa;oBAAC,MAAK;oBAAS,aAAa;oBAAW,OAAO,EAAE;;;;;;;;;;;;;;;;;AAItE;GAhFwB;;QACZ,yMAAA,CAAA,kBAAe;QAEc,6JAAA,CAAA,iBAAc;;;KAH/B", "debugId": null}}, {"offset": {"line": 773, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/auth/LoginDialog.jsx"], "sourcesContent": ["\"use client\";\r\nimport { useTranslations } from \"next-intl\";\r\nimport { Link } from \"@/i18n/navigation\";\r\nimport { Dialog, DialogContent, DialogHeader, DialogTitle, DialogClose } from \"@/components/ui/dialog\";\r\nimport LoginForm from \"@/components/auth/LoginForm\";\r\n\r\nfunction LoginDialog({ open, onOpenChange }) {\r\n  const t = useTranslations(\"PropertyList\");\r\n\r\n  return (\r\n    <Dialog open={open} onOpenChange={onOpenChange}>\r\n      <DialogContent className=\"sm:max-w-md\">\r\n        <DialogHeader>\r\n          <DialogTitle>{t(\"loginRequired\")}</DialogTitle>          \r\n        </DialogHeader>\r\n        <div className=\"px-6 py-4\">\r\n          <LoginForm />\r\n          <div className=\"mt-4 text-center\">\r\n            <p className=\"text-sm text-gray-600\">\r\n              {t(\"dontHaveAccount\")}{\" \"}\r\n              <Link href=\"/dang-ky\" className=\"text-coral-500 hover:text-coral-600 font-medium\">\r\n                {t(\"signUpHere\")}\r\n              </Link>\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n\r\nexport default LoginDialog; "], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;;;AAJA;;;;;AAMA,SAAS,YAAY,EAAE,IAAI,EAAE,YAAY,EAAE;;IACzC,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAE1B,qBACE,6LAAC,8HAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,6LAAC,8HAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,6LAAC,8HAAA,CAAA,eAAY;8BACX,cAAA,6LAAC,8HAAA,CAAA,cAAW;kCAAE,EAAE;;;;;;;;;;;8BAElB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mIAAA,CAAA,UAAS;;;;;sCACV,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;;oCACV,EAAE;oCAAoB;kDACvB,6LAAC,qHAAA,CAAA,OAAI;wCAAC,MAAK;wCAAW,WAAU;kDAC7B,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnB;GAvBS;;QACG,yMAAA,CAAA,kBAAe;;;KADlB;uCAyBM", "debugId": null}}, {"offset": {"line": 881, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/lib/tracking.ts"], "sourcesContent": ["// lib/tracking.ts\r\nimport { v4 as uuidv4 } from 'uuid';\r\nimport Bowser from 'bowser';\r\nimport Cookies from 'js-cookie';\r\n\r\nconst API_BASE_URL = `${process.env.NEXT_PUBLIC_API_URL}/api/log`;\r\n\r\n/**\r\n * Get or create session ID (stored in cookie)\r\n */\r\nfunction getOrCreateSessionId(): string {\r\n  let sessionId = Cookies.get('session_id');\r\n  if (!sessionId) {\r\n    sessionId = uuidv4();\r\n    Cookies.set('session_id', sessionId, { expires: 1 }); // 1 day session\r\n  }\r\n  return sessionId;\r\n}\r\n\r\n/**\r\n * Get or create device ID (stored in localStorage)\r\n */\r\nexport function getOrCreateDeviceId(): string {\r\n  if (typeof window === 'undefined') return 'unknown';\r\n\r\n  let deviceId = localStorage.getItem('device_id');\r\n  if (!deviceId) {\r\n    deviceId = uuidv4();\r\n    localStorage.setItem('device_id', deviceId);\r\n  }\r\n  return deviceId;\r\n}\r\n\r\n/**\r\n * Extract device information using Bowser\r\n */\r\nexport function getDeviceInfo() {\r\n  if (typeof window === 'undefined') {\r\n    return {\r\n      userAgent: 'unknown',\r\n      sessionId: 'unknown',\r\n      deviceType: 'unknown',\r\n      platform: 'unknown',\r\n      browser: 'unknown',\r\n      deviceId: 'unknown'\r\n    };\r\n  }\r\n\r\n  const parser = Bowser.getParser(window.navigator.userAgent);\r\n  return {\r\n    userAgent: window.navigator.userAgent,\r\n    sessionId: getOrCreateSessionId(),\r\n    deviceType: parser.getPlatformType(true),\r\n    platform: parser.getOSName(true),\r\n    browser: parser.getBrowserName(true),\r\n    deviceId: getOrCreateDeviceId(),\r\n  };\r\n}\r\n\r\n/**\r\n * Send property view log to backend (PropertyViewLog)\r\n */\r\nexport async function logPropertyView(propertyId: string) {\r\n  try {\r\n    const device = getDeviceInfo();\r\n\r\n    await fetch(`${API_BASE_URL}/property-view`, {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify({\r\n        propertyId,\r\n        ...device\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error('[logPropertyView] Failed:', error);\r\n  }\r\n}\r\n\r\n/**\r\n * Send property engagement event (PropertyEngagementEvents)\r\n */\r\nexport async function logPropertyEvent(\r\n  propertyId: string,\r\n  eventType:\r\n    | 'click_phone'\r\n    | 'chat'\r\n    | 'favorite'\r\n    | 'unfavorite'\r\n    | 'submit_contact_form'\r\n    | 'click_map'\r\n    | 'share'\r\n) {\r\n  try {\r\n    const device = getDeviceInfo();\r\n\r\n    await fetch(`${API_BASE_URL}/property-event`, {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify({\r\n        propertyId,\r\n        eventType,\r\n        ...device\r\n      }),\r\n    });\r\n  } catch (error) {\r\n    console.error(`[logPropertyEvent:${eventType}] Failed:`, error);\r\n  }\r\n}\r\n\r\n/**\r\n * Throttle helper (to prevent over-logging)\r\n */\r\nexport function throttle<T extends (...args: any[]) => void>(\r\n  func: T,\r\n  limitMs: number\r\n): (...args: Parameters<T>) => void {\r\n  let lastRun = 0;\r\n  return function (...args: Parameters<T>) {\r\n    const now = Date.now();\r\n    if (now - lastRun >= limitMs) {\r\n      lastRun = now;\r\n      func(...args);\r\n    }\r\n  };\r\n}\r\n\r\n/**\r\n * Optional future: search impression log\r\n */\r\nexport async function logSearchImpression(propertyIds: string[]) {\r\n  try {\r\n    await fetch(`${API_BASE_URL}/search-impression`, {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify({ propertyIds }),\r\n    });\r\n  } catch (error) {\r\n    console.error('[logSearchImpression] Failed:', error);\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,kBAAkB;;;;;;;;;AAKM;AAJxB;AACA;AACA;;;;AAEA,MAAM,eAAe,8DAAmC,QAAQ,CAAC;AAEjE;;CAEC,GACD,SAAS;IACP,IAAI,YAAY,wJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;IAC5B,IAAI,CAAC,WAAW;QACd,YAAY,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QACjB,wJAAA,CAAA,UAAO,CAAC,GAAG,CAAC,cAAc,WAAW;YAAE,SAAS;QAAE,IAAI,gBAAgB;IACxE;IACA,OAAO;AACT;AAKO,SAAS;IACd,uCAAmC;;IAAgB;IAEnD,IAAI,WAAW,aAAa,OAAO,CAAC;IACpC,IAAI,CAAC,UAAU;QACb,WAAW,CAAA,GAAA,wLAAA,CAAA,KAAM,AAAD;QAChB,aAAa,OAAO,CAAC,aAAa;IACpC;IACA,OAAO;AACT;AAKO,SAAS;IACd,uCAAmC;;IASnC;IAEA,MAAM,SAAS,gIAAA,CAAA,UAAM,CAAC,SAAS,CAAC,OAAO,SAAS,CAAC,SAAS;IAC1D,OAAO;QACL,WAAW,OAAO,SAAS,CAAC,SAAS;QACrC,WAAW;QACX,YAAY,OAAO,eAAe,CAAC;QACnC,UAAU,OAAO,SAAS,CAAC;QAC3B,SAAS,OAAO,cAAc,CAAC;QAC/B,UAAU;IACZ;AACF;AAKO,eAAe,gBAAgB,UAAkB;IACtD,IAAI;QACF,MAAM,SAAS;QAEf,MAAM,MAAM,GAAG,aAAa,cAAc,CAAC,EAAE;YAC3C,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB;gBACA,GAAG,MAAM;YACX;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;IAC7C;AACF;AAKO,eAAe,iBACpB,UAAkB,EAClB,SAOW;IAEX,IAAI;QACF,MAAM,SAAS;QAEf,MAAM,MAAM,GAAG,aAAa,eAAe,CAAC,EAAE;YAC5C,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB;gBACA;gBACA,GAAG,MAAM;YACX;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,kBAAkB,EAAE,UAAU,SAAS,CAAC,EAAE;IAC3D;AACF;AAKO,SAAS,SACd,IAAO,EACP,OAAe;IAEf,IAAI,UAAU;IACd,OAAO,SAAU,GAAG,IAAmB;QACrC,MAAM,MAAM,KAAK,GAAG;QACpB,IAAI,MAAM,WAAW,SAAS;YAC5B,UAAU;YACV,QAAQ;QACV;IACF;AACF;AAKO,eAAe,oBAAoB,WAAqB;IAC7D,IAAI;QACF,MAAM,MAAM,GAAG,aAAa,kBAAkB,CAAC,EAAE;YAC/C,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAY;QACrC;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;IACjD;AACF", "debugId": null}}, {"offset": {"line": 1004, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/components/property/PropertyDetailModal.jsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport { Di<PERSON>, DialogContent, DialogTitle } from \"@/components/ui/dialog\";\r\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\r\nimport { ChevronLeft, Heart, Share, X, Send } from \"lucide-react\";\r\nimport Image from \"next/image\";\r\nimport dynamic from \"next/dynamic\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { useTranslations } from \"next-intl\";\r\nimport { useToast } from \"@/hooks/use-toast\";\r\nimport { useAuth } from \"@/contexts/AuthContext\";\r\nimport { addToFavorites, removeFromFavorites } from \"@/app/actions/server/userFavorite\";\r\nimport LoginDialog from \"@/components/auth/LoginDialog\";\r\nimport { PropertyEventType } from \"@/lib/enum\";\r\nimport { logPropertyEvent, logPropertyView } from \"@/lib/tracking\";\r\n\r\n// Dynamically import components\r\nconst NearbyPropertiesCarousel = dynamic(() => import(\"./NearbyPropertiesCarousel\"), {\r\n  loading: () => <div className=\"h-16 bg-white animate-pulse\"></div>,\r\n});\r\n\r\nconst PropertyImageGallery = dynamic(() => import(\"./PropertyImageGallery\"), {\r\n  loading: () => <div className=\"h-64 bg-gray-100 animate-pulse rounded-md\"></div>,\r\n});\r\n\r\nconst PropertyDescription = dynamic(() => import(\"./PropertyDescription\"), {\r\n  loading: () => <div className=\"h-64 bg-white animate-pulse\"></div>,\r\n});\r\n\r\nconst PropertyContactForm = dynamic(() => import(\"./PropertyContactForm\"), {\r\n  ssr: false,\r\n});\r\n\r\nconst DetailMap = dynamic(() => import(\"./DetailMap\"), {\r\n  ssr: false,\r\n  loading: () => <div className=\"h-64 bg-gray-100 animate-pulse rounded-md\"></div>,\r\n});\r\n\r\nconst ShareModal = dynamic(() => import(\"../user-property/ShareModal\"), {\r\n  ssr: false,\r\n});\r\n\r\nexport default function PropertyDetailModal({ property, onClose, isFavorite = false, onToggleFavorite }) {\r\n  const [isOpen, setIsOpen] = useState(true);\r\n  const [isContactModalOpen, setIsContactModalOpen] = useState(false);\r\n  const [isShareModalOpen, setIsShareModalOpen] = useState(false);\r\n  const [favorite, setFavorite] = useState(isFavorite);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [showLoginDialog, setShowLoginDialog] = useState(false);\r\n  const tCommon = useTranslations(\"Common\");\r\n  const t = useTranslations(\"PropertyCard\");\r\n  const { toast } = useToast();\r\n  const { isLoggedIn } = useAuth();\r\n\r\n  // Update local favorite state when prop changes\r\n  useEffect(() => {\r\n    setFavorite(isFavorite);\r\n  }, [isFavorite]);\r\n\r\n  useEffect(() => {\r\n    if (property && property?.id) {\r\n      logPropertyView(property.id);\r\n    } else {\r\n      console.error(\"Property is not defined\");\r\n    }\r\n  }, [property?.id]);\r\n\r\n  const handleFavoriteClick = async (e) => {\r\n    e.stopPropagation();\r\n    if (!isLoggedIn) {\r\n      setShowLoginDialog(true);\r\n      return;\r\n    }\r\n\r\n    setIsLoading(true);\r\n    try {\r\n      const newFavoriteStatus = !favorite;\r\n\r\n      if (newFavoriteStatus) {\r\n        logPropertyEvent(property.id, PropertyEventType.FAVORITE);\r\n      } else {\r\n        logPropertyEvent(property.id, PropertyEventType.UNFAVORITE);\r\n      }\r\n\r\n      const result = newFavoriteStatus ? await addToFavorites(property.id) : await removeFromFavorites(property.id);\r\n\r\n      if (result.success) {\r\n        setFavorite(newFavoriteStatus);\r\n        if (onToggleFavorite) {\r\n          onToggleFavorite(property.id, newFavoriteStatus);\r\n        }\r\n        toast({\r\n          title: newFavoriteStatus ? t(\"addedToFavorites\") : t(\"removedFromFavorites\"),\r\n          description: newFavoriteStatus ? t(\"addedToFavoritesDesc\") : t(\"removedFromFavoritesDesc\"),\r\n          variant: \"default\",\r\n        });\r\n      } else {\r\n        toast({\r\n          title: t(\"errorOccurred\"),\r\n          description: result.message || t(\"cannotUpdateFavorite\"),\r\n          variant: \"destructive\",\r\n        });\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error toggling favorite:\", error);\r\n      toast({\r\n        title: t(\"errorOccurred\"),\r\n        description: t(\"cannotUpdateFavorite\"),\r\n        variant: \"destructive\",\r\n      });\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleClose = () => {\r\n    setIsOpen(false);\r\n    if (onClose) onClose();\r\n  };\r\n\r\n  const handleContactModalClose = () => {\r\n    setIsContactModalOpen(false);\r\n  };\r\n\r\n  const handleShareClick = () => {\r\n    setIsShareModalOpen(true);\r\n    logPropertyEvent(property.id, PropertyEventType.SHARE);\r\n  };\r\n\r\n  const handleShareModalClose = () => {\r\n    setIsShareModalOpen(false);\r\n  };\r\n\r\n  const handleContactClick = () => {\r\n    setIsContactModalOpen(true);\r\n    logPropertyEvent(property.id, PropertyEventType.CLICK_PHONE);\r\n  };\r\n\r\n  let formattedAddress = property.address || \"\";\r\n  let center = { latitude: property.latitude, longitude: property.longitude };\r\n\r\n  try {\r\n    if (property.placeData) {\r\n      const placeData = JSON.parse(property.placeData);\r\n      if (placeData.result && placeData.result.formatted_address) {\r\n        formattedAddress = placeData.result.formatted_address;\r\n      }\r\n    }\r\n  } catch (e) {\r\n    console.error(\"Error parsing placeData:\", e);\r\n  }\r\n\r\n  const content = (\r\n    <div className=\"bg-white mx-3\">\r\n      {/* Header */}\r\n      <header className=\"sticky top-0 z-10 bg-white p-4 border-b flex items-center justify-between\">\r\n        <button onClick={onClose ? handleClose : () => window.history.back()} className=\"flex items-center text-gray-600\">\r\n          <ChevronLeft className=\"h-5 w-5 mr-2\" />\r\n          <span>Quay lại</span>\r\n        </button>\r\n        <div className=\"flex-1 flex justify-center\">\r\n          <Image src=\"/yezhome_logo.png\" alt=\"YEZ Home\" width={120} height={120} />\r\n        </div>\r\n        <div className=\"flex items-center gap-3\">\r\n          <button\r\n            className={`flex items-center gap-1 hover:text-teal-600 transition-colors ${favorite ? \"text-coral-500\" : \"text-gray-700\"}`}\r\n            onClick={handleFavoriteClick}\r\n            disabled={isLoading}\r\n          >\r\n            <Heart className={`h-5 w-5 ${isLoading ? \"animate-pulse\" : \"\"}`} fill={favorite ? \"currentColor\" : \"none\"} />\r\n            <span className=\"hidden sm:inline\">Lưu</span>\r\n          </button>\r\n          <button className=\"flex items-center gap-1 text-gray-700 hover:text-teal-600 transition-colors\" onClick={handleShareClick}>\r\n            <Share className=\"h-5 w-5\" />\r\n            <span className=\"hidden sm:inline\">Chia sẻ</span>\r\n          </button>\r\n          <button\r\n            onClick={onClose ? handleClose : () => window.history.back()}\r\n            className=\"flex items-center gap-1 text-gray-700 hover:text-teal-600 transition-colors\"\r\n          >\r\n            <X className=\"h-5 w-5\" />\r\n            <span className=\"hidden sm:inline\">Đóng</span>\r\n          </button>\r\n        </div>\r\n      </header>\r\n\r\n      {/* Property Images */}\r\n      <div className=\"p-4\">\r\n        <PropertyImageGallery images={property.propertyMedia?.map((pm) => pm.mediaURL) || []} propertyName={property.name} />\r\n      </div>\r\n\r\n      {/* Property Details */}\r\n      <div className=\"p-4 grid grid-cols-1 lg:grid-cols-4 gap-6\">\r\n        <div className=\"lg:col-span-3\">\r\n          <PropertyDescription\r\n            property={{\r\n              ...property,\r\n              address: formattedAddress,\r\n              propertyType: property.propertyType ? tCommon(`propertyType_${property.propertyType}`) : \"\",\r\n              postType: property.postType ? tCommon(`propertyPostType_${property.postType}`) : \"\",\r\n              direction: property.direction ? tCommon(`${property.direction}`) : \"__\",\r\n              balconyDirection: property.balconyDirection ? tCommon(`${property.balconyDirection}`) : \"__\",\r\n              legality: property.legality ? tCommon(`legality_${property.legality}`) : \"__\",\r\n              interior: property.interior ? tCommon(`interior_${property.interior}`) : \"__\",\r\n            }}\r\n          />\r\n          {/* Map */}\r\n          <div className=\"p-4\">\r\n            <h2 className=\"text-2xl font-bold mb-4\">Vị trí bất động sản</h2>\r\n            <DetailMap property={property} center={center} />\r\n          </div>\r\n\r\n          {/* Nearby Properties Section */}\r\n          <div className=\"p-4\">\r\n            <div className=\"border-t pt-6\">\r\n              <h2 className=\"text-2xl font-bold mb-4\">Bất động sản lân cận</h2>\r\n              <NearbyPropertiesCarousel latitude={property.latitude} longitude={property.longitude} tCommon={tCommon} />\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Contact Section */}\r\n        <div className=\"lg:col-span-1\">\r\n          <div className=\"bg-white border rounded-md p-4 sticky top-20\">\r\n            <button\r\n              className=\"w-full bg-teal-600 text-white hover:bg-teal-700 font-semibold py-3 px-3 rounded-md mb-3 text-lg flex items-center justify-center gap-2\"\r\n              onClick={handleContactClick}\r\n              type=\"button\"\r\n            >\r\n              <Send /> Liên hệ người bán\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n\r\n  // If this is being used as a page view (no onClose provided), render without Dialog\r\n  if (!onClose) {\r\n    return content;\r\n  }\r\n\r\n  // Modal view with Dialog\r\n  return (\r\n    <>\r\n      <Dialog open={isOpen} onOpenChange={handleClose}>\r\n        <DialogContent className=\"max-w-7xl p-0 h-[95vh] w-[90vw] [&>button:last-child]:hidden\" onPointerDownOutside={(e) => e.preventDefault()}>\r\n          <DialogTitle className=\"sr-only\">{property.name || \"Property Details\"}</DialogTitle>\r\n          <ScrollArea className=\"h-[94vh]\">{content}</ScrollArea>\r\n        </DialogContent>\r\n      </Dialog>\r\n\r\n      {/* Contact Form Modal */}\r\n      <PropertyContactForm isOpen={isContactModalOpen} onClose={handleContactModalClose} propertyId={property.id} ownerId={property.ownerId} />\r\n\r\n      {/* Share Modal */}\r\n      <ShareModal open={isShareModalOpen} onClose={handleShareModalClose} property={property} />\r\n\r\n      {/* Login Dialog */}\r\n      <LoginDialog open={showLoginDialog} onOpenChange={setShowLoginDialog} />\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;;;;;;;;;AAfA;;;;;;;;;;;;;;;AAiBA,gCAAgC;AAChC,MAAM,2BAA2B,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;;;;;;IACvC,SAAS,kBAAM,6LAAC;YAAI,WAAU;;;;;;;KAD1B;AAIN,MAAM,uBAAuB,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;;;;;;IACnC,SAAS,kBAAM,6LAAC;YAAI,WAAU;;;;;;;MAD1B;AAIN,MAAM,sBAAsB,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;;;;;;IAClC,SAAS,kBAAM,6LAAC;YAAI,WAAU;;;;;;;MAD1B;AAIN,MAAM,sBAAsB,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;;;;;;IAClC,KAAK;;MADD;AAIN,MAAM,YAAY,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;;;;;;IACxB,KAAK;IACL,SAAS,kBAAM,6LAAC;YAAI,WAAU;;;;;;;MAF1B;AAKN,MAAM,aAAa,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;;;;;;IACzB,KAAK;;MADD;AAIS,SAAS,oBAAoB,EAAE,QAAQ,EAAE,OAAO,EAAE,aAAa,KAAK,EAAE,gBAAgB,EAAE;;IACrG,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,UAAU,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAChC,MAAM,IAAI,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;IAC1B,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IAE7B,gDAAgD;IAChD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,YAAY;QACd;wCAAG;QAAC;KAAW;IAEf,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,IAAI,YAAY,UAAU,IAAI;gBAC5B,CAAA,GAAA,kHAAA,CAAA,kBAAe,AAAD,EAAE,SAAS,EAAE;YAC7B,OAAO;gBACL,QAAQ,KAAK,CAAC;YAChB;QACF;wCAAG;QAAC,UAAU;KAAG;IAEjB,MAAM,sBAAsB,OAAO;QACjC,EAAE,eAAe;QACjB,IAAI,CAAC,YAAY;YACf,mBAAmB;YACnB;QACF;QAEA,aAAa;QACb,IAAI;YACF,MAAM,oBAAoB,CAAC;YAE3B,IAAI,mBAAmB;gBACrB,CAAA,GAAA,kHAAA,CAAA,mBAAgB,AAAD,EAAE,SAAS,EAAE,EAAE,8GAAA,CAAA,oBAAiB,CAAC,QAAQ;YAC1D,OAAO;gBACL,CAAA,GAAA,kHAAA,CAAA,mBAAgB,AAAD,EAAE,SAAS,EAAE,EAAE,8GAAA,CAAA,oBAAiB,CAAC,UAAU;YAC5D;YAEA,MAAM,SAAS,oBAAoB,MAAM,CAAA,GAAA,mKAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,EAAE,IAAI,MAAM,CAAA,GAAA,mKAAA,CAAA,sBAAmB,AAAD,EAAE,SAAS,EAAE;YAE5G,IAAI,OAAO,OAAO,EAAE;gBAClB,YAAY;gBACZ,IAAI,kBAAkB;oBACpB,iBAAiB,SAAS,EAAE,EAAE;gBAChC;gBACA,MAAM;oBACJ,OAAO,oBAAoB,EAAE,sBAAsB,EAAE;oBACrD,aAAa,oBAAoB,EAAE,0BAA0B,EAAE;oBAC/D,SAAS;gBACX;YACF,OAAO;gBACL,MAAM;oBACJ,OAAO,EAAE;oBACT,aAAa,OAAO,OAAO,IAAI,EAAE;oBACjC,SAAS;gBACX;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;gBACJ,OAAO,EAAE;gBACT,aAAa,EAAE;gBACf,SAAS;YACX;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,cAAc;QAClB,UAAU;QACV,IAAI,SAAS;IACf;IAEA,MAAM,0BAA0B;QAC9B,sBAAsB;IACxB;IAEA,MAAM,mBAAmB;QACvB,oBAAoB;QACpB,CAAA,GAAA,kHAAA,CAAA,mBAAgB,AAAD,EAAE,SAAS,EAAE,EAAE,8GAAA,CAAA,oBAAiB,CAAC,KAAK;IACvD;IAEA,MAAM,wBAAwB;QAC5B,oBAAoB;IACtB;IAEA,MAAM,qBAAqB;QACzB,sBAAsB;QACtB,CAAA,GAAA,kHAAA,CAAA,mBAAgB,AAAD,EAAE,SAAS,EAAE,EAAE,8GAAA,CAAA,oBAAiB,CAAC,WAAW;IAC7D;IAEA,IAAI,mBAAmB,SAAS,OAAO,IAAI;IAC3C,IAAI,SAAS;QAAE,UAAU,SAAS,QAAQ;QAAE,WAAW,SAAS,SAAS;IAAC;IAE1E,IAAI;QACF,IAAI,SAAS,SAAS,EAAE;YACtB,MAAM,YAAY,KAAK,KAAK,CAAC,SAAS,SAAS;YAC/C,IAAI,UAAU,MAAM,IAAI,UAAU,MAAM,CAAC,iBAAiB,EAAE;gBAC1D,mBAAmB,UAAU,MAAM,CAAC,iBAAiB;YACvD;QACF;IACF,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,4BAA4B;IAC5C;IAEA,MAAM,wBACJ,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;;kCAChB,6LAAC;wBAAO,SAAS,UAAU,cAAc,IAAM,OAAO,OAAO,CAAC,IAAI;wBAAI,WAAU;;0CAC9E,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,6LAAC;0CAAK;;;;;;;;;;;;kCAER,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4BAAC,KAAI;4BAAoB,KAAI;4BAAW,OAAO;4BAAK,QAAQ;;;;;;;;;;;kCAEpE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,WAAW,CAAC,8DAA8D,EAAE,WAAW,mBAAmB,iBAAiB;gCAC3H,SAAS;gCACT,UAAU;;kDAEV,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAW,CAAC,QAAQ,EAAE,YAAY,kBAAkB,IAAI;wCAAE,MAAM,WAAW,iBAAiB;;;;;;kDACnG,6LAAC;wCAAK,WAAU;kDAAmB;;;;;;;;;;;;0CAErC,6LAAC;gCAAO,WAAU;gCAA8E,SAAS;;kDACvG,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;wCAAK,WAAU;kDAAmB;;;;;;;;;;;;0CAErC,6LAAC;gCACC,SAAS,UAAU,cAAc,IAAM,OAAO,OAAO,CAAC,IAAI;gCAC1D,WAAU;;kDAEV,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;kDACb,6LAAC;wCAAK,WAAU;kDAAmB;;;;;;;;;;;;;;;;;;;;;;;;0BAMzC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAqB,QAAQ,SAAS,aAAa,EAAE,IAAI,CAAC,KAAO,GAAG,QAAQ,KAAK,EAAE;oBAAE,cAAc,SAAS,IAAI;;;;;;;;;;;0BAInH,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,UAAU;oCACR,GAAG,QAAQ;oCACX,SAAS;oCACT,cAAc,SAAS,YAAY,GAAG,QAAQ,CAAC,aAAa,EAAE,SAAS,YAAY,EAAE,IAAI;oCACzF,UAAU,SAAS,QAAQ,GAAG,QAAQ,CAAC,iBAAiB,EAAE,SAAS,QAAQ,EAAE,IAAI;oCACjF,WAAW,SAAS,SAAS,GAAG,QAAQ,GAAG,SAAS,SAAS,EAAE,IAAI;oCACnE,kBAAkB,SAAS,gBAAgB,GAAG,QAAQ,GAAG,SAAS,gBAAgB,EAAE,IAAI;oCACxF,UAAU,SAAS,QAAQ,GAAG,QAAQ,CAAC,SAAS,EAAE,SAAS,QAAQ,EAAE,IAAI;oCACzE,UAAU,SAAS,QAAQ,GAAG,QAAQ,CAAC,SAAS,EAAE,SAAS,QAAQ,EAAE,IAAI;gCAC3E;;;;;;0CAGF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA0B;;;;;;kDACxC,6LAAC;wCAAU,UAAU;wCAAU,QAAQ;;;;;;;;;;;;0CAIzC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA0B;;;;;;sDACxC,6LAAC;4CAAyB,UAAU,SAAS,QAAQ;4CAAE,WAAW,SAAS,SAAS;4CAAE,SAAS;;;;;;;;;;;;;;;;;;;;;;;kCAMrG,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,WAAU;gCACV,SAAS;gCACT,MAAK;;kDAEL,6LAAC,qMAAA,CAAA,OAAI;;;;;oCAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQpB,oFAAoF;IACpF,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,yBAAyB;IACzB,qBACE;;0BACE,6LAAC,8HAAA,CAAA,SAAM;gBAAC,MAAM;gBAAQ,cAAc;0BAClC,cAAA,6LAAC,8HAAA,CAAA,gBAAa;oBAAC,WAAU;oBAA+D,sBAAsB,CAAC,IAAM,EAAE,cAAc;;sCACnI,6LAAC,8HAAA,CAAA,cAAW;4BAAC,WAAU;sCAAW,SAAS,IAAI,IAAI;;;;;;sCACnD,6LAAC,sIAAA,CAAA,aAAU;4BAAC,WAAU;sCAAY;;;;;;;;;;;;;;;;;0BAKtC,6LAAC;gBAAoB,QAAQ;gBAAoB,SAAS;gBAAyB,YAAY,SAAS,EAAE;gBAAE,SAAS,SAAS,OAAO;;;;;;0BAGrI,6LAAC;gBAAW,MAAM;gBAAkB,SAAS;gBAAuB,UAAU;;;;;;0BAG9E,6LAAC,qIAAA,CAAA,UAAW;gBAAC,MAAM;gBAAiB,cAAc;;;;;;;;AAGxD;GA5NwB;;QAON,yMAAA,CAAA,kBAAe;QACrB,yMAAA,CAAA,kBAAe;QACP,wHAAA,CAAA,WAAQ;QACH,2HAAA,CAAA,UAAO;;;MAVR", "debugId": null}}]}