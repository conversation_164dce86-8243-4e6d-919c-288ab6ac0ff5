{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Project/YEZ_Tech/YEZ_Home/YEZHome_FE/app/services/payment.js"], "sourcesContent": ["/**\r\n * Payment gateway integration service\r\n */\r\n\r\n// Configuration would normally come from environment variables\r\nconst PAYMENT_CONFIG = {\r\n  momo: {\r\n    partnerId: process.env.MOMO_PARTNER_ID || \"MOMO_PARTNER_ID\",\r\n    partnerCode: process.env.MOMO_PARTNER_CODE || \"MOMO_PARTNER_CODE\",\r\n    accessKey: process.env.MOMO_ACCESS_KEY || \"MOMO_ACCESS_KEY\",\r\n    secretKey: process.env.MOMO_SECRET_KEY || \"MOMO_SECRET_KEY\",\r\n    endpoint: process.env.MOMO_ENDPOINT || \"https://test-payment.momo.vn/v2/gateway/api/create\",\r\n  }\r\n  // Add configuration for other payment methods like card processing\r\n};\r\n\r\n/**\r\n * Create a payment with MoMo\r\n */\r\nexport async function createMomoPayment(amount, orderId, returnUrl, notifyUrl, extraData = {}) {\r\n  try {\r\n    const requestId = `${Date.now()}_${Math.floor(Math.random() * 1000)}`;\r\n    const orderInfo = `Nạp tiền vào ví #${orderId}`;\r\n    \r\n    // Create signature based on MoMo's requirements\r\n    const rawSignature = `partnerCode=${PAYMENT_CONFIG.momo.partnerCode}&accessKey=${PAYMENT_CONFIG.momo.accessKey}&requestId=${requestId}&amount=${amount}&orderId=${orderId}&orderInfo=${orderInfo}&returnUrl=${returnUrl}&notifyUrl=${notifyUrl}&extraData=${JSON.stringify(extraData)}`;\r\n    \r\n    // In production, use crypto library to create HMAC SHA256 signature\r\n    const signature = await createHmacSignature(rawSignature, PAYMENT_CONFIG.momo.secretKey);\r\n    \r\n    const response = await fetch(PAYMENT_CONFIG.momo.endpoint, {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify({\r\n        partnerCode: PAYMENT_CONFIG.momo.partnerCode,\r\n        accessKey: PAYMENT_CONFIG.momo.accessKey,\r\n        requestId: requestId,\r\n        amount: amount,\r\n        orderId: orderId,\r\n        orderInfo: orderInfo,\r\n        returnUrl: returnUrl,\r\n        notifyUrl: notifyUrl,\r\n        extraData: JSON.stringify(extraData),\r\n        requestType: \"captureWallet\",\r\n        signature: signature,\r\n      }),\r\n    });\r\n    \r\n    const result = await response.json();\r\n    \r\n    if (result.resultCode === 0) {\r\n      // Success - return payment URL\r\n      return {\r\n        success: true,\r\n        paymentUrl: result.payUrl,\r\n        orderId: orderId,\r\n        transactionId: result.transactionId,\r\n      };\r\n    } else {\r\n      return {\r\n        success: false,\r\n        error: result.message || \"Không thể tạo giao dịch MoMo\",\r\n        code: result.resultCode,\r\n      };\r\n    }\r\n  } catch (error) {\r\n    console.error(\"MoMo payment error:\", error);\r\n    return {\r\n      success: false,\r\n      error: \"Đã xảy ra lỗi khi kết nối với MoMo\",\r\n    };\r\n  }\r\n}\r\n\r\n/**\r\n * Verify MoMo payment status\r\n */\r\nexport async function verifyMomoPayment(orderId) {\r\n  try {\r\n    // In a real implementation, you would query MoMo's API to check payment status\r\n    const requestId = `${Date.now()}_verify`;\r\n    \r\n    // Create signature\r\n    const rawSignature = `partnerCode=${PAYMENT_CONFIG.momo.partnerCode}&accessKey=${PAYMENT_CONFIG.momo.accessKey}&requestId=${requestId}&orderId=${orderId}`;\r\n    const signature = await createHmacSignature(rawSignature, PAYMENT_CONFIG.momo.secretKey);\r\n    \r\n    const response = await fetch(`${PAYMENT_CONFIG.momo.endpoint}/query`, {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify({\r\n        partnerCode: PAYMENT_CONFIG.momo.partnerCode,\r\n        accessKey: PAYMENT_CONFIG.momo.accessKey,\r\n        requestId: requestId,\r\n        orderId: orderId,\r\n        signature: signature,\r\n      }),\r\n    });\r\n    \r\n    const result = await response.json();\r\n    \r\n    return {\r\n      success: result.resultCode === 0,\r\n      verified: result.resultCode === 0 && result.transactionStatus === 0,\r\n      amount: result.amount,\r\n      transactionId: result.transId,\r\n      message: result.message,\r\n    };\r\n  } catch (error) {\r\n    console.error(\"MoMo verification error:\", error);\r\n    return { success: false, error: \"Verification failed\" };\r\n  }\r\n}\r\n\r\n/**\r\n * Helper function to create HMAC SHA256 signatures\r\n */\r\nasync function createHmacSignature(message, secretKey) {\r\n  // In browser environment, use SubtleCrypto API\r\n  // In Node.js environment, use the crypto module\r\n  // This is a simplified example\r\n  const encoder = new TextEncoder();\r\n  const keyData = encoder.encode(secretKey);\r\n  const messageData = encoder.encode(message);\r\n  \r\n  const cryptoKey = await crypto.subtle.importKey(\r\n    'raw',\r\n    keyData,\r\n    { name: 'HMAC', hash: 'SHA-256' },\r\n    false,\r\n    ['sign']\r\n  );\r\n  \r\n  const signature = await crypto.subtle.sign(\r\n    'HMAC',\r\n    cryptoKey,\r\n    messageData\r\n  );\r\n  \r\n  // Convert to hex string\r\n  return Array.from(new Uint8Array(signature))\r\n    .map(b => b.toString(16).padStart(2, '0'))\r\n    .join('');\r\n}"], "names": [], "mappings": "AAAA;;CAEC,GAED,+DAA+D;;;;;AAC/D,MAAM,iBAAiB;IACrB,MAAM;QACJ,WAAW,QAAQ,GAAG,CAAC,eAAe,IAAI;QAC1C,aAAa,QAAQ,GAAG,CAAC,iBAAiB,IAAI;QAC9C,WAAW,QAAQ,GAAG,CAAC,eAAe,IAAI;QAC1C,WAAW,QAAQ,GAAG,CAAC,eAAe,IAAI;QAC1C,UAAU,QAAQ,GAAG,CAAC,aAAa,IAAI;IACzC;AAEF;AAKO,eAAe,kBAAkB,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,YAAY,CAAC,CAAC;IAC3F,IAAI;QACF,MAAM,YAAY,GAAG,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO;QACrE,MAAM,YAAY,CAAC,iBAAiB,EAAE,SAAS;QAE/C,gDAAgD;QAChD,MAAM,eAAe,CAAC,YAAY,EAAE,eAAe,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,eAAe,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,UAAU,QAAQ,EAAE,OAAO,SAAS,EAAE,QAAQ,WAAW,EAAE,UAAU,WAAW,EAAE,UAAU,WAAW,EAAE,UAAU,WAAW,EAAE,KAAK,SAAS,CAAC,YAAY;QAEvR,oEAAoE;QACpE,MAAM,YAAY,MAAM,oBAAoB,cAAc,eAAe,IAAI,CAAC,SAAS;QAEvF,MAAM,WAAW,MAAM,MAAM,eAAe,IAAI,CAAC,QAAQ,EAAE;YACzD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,aAAa,eAAe,IAAI,CAAC,WAAW;gBAC5C,WAAW,eAAe,IAAI,CAAC,SAAS;gBACxC,WAAW;gBACX,QAAQ;gBACR,SAAS;gBACT,WAAW;gBACX,WAAW;gBACX,WAAW;gBACX,WAAW,KAAK,SAAS,CAAC;gBAC1B,aAAa;gBACb,WAAW;YACb;QACF;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAElC,IAAI,OAAO,UAAU,KAAK,GAAG;YAC3B,+BAA+B;YAC/B,OAAO;gBACL,SAAS;gBACT,YAAY,OAAO,MAAM;gBACzB,SAAS;gBACT,eAAe,OAAO,aAAa;YACrC;QACF,OAAO;YACL,OAAO;gBACL,SAAS;gBACT,OAAO,OAAO,OAAO,IAAI;gBACzB,MAAM,OAAO,UAAU;YACzB;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO;YACL,SAAS;YACT,OAAO;QACT;IACF;AACF;AAKO,eAAe,kBAAkB,OAAO;IAC7C,IAAI;QACF,+EAA+E;QAC/E,MAAM,YAAY,GAAG,KAAK,GAAG,GAAG,OAAO,CAAC;QAExC,mBAAmB;QACnB,MAAM,eAAe,CAAC,YAAY,EAAE,eAAe,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,eAAe,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,UAAU,SAAS,EAAE,SAAS;QAC1J,MAAM,YAAY,MAAM,oBAAoB,cAAc,eAAe,IAAI,CAAC,SAAS;QAEvF,MAAM,WAAW,MAAM,MAAM,GAAG,eAAe,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;YACpE,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,aAAa,eAAe,IAAI,CAAC,WAAW;gBAC5C,WAAW,eAAe,IAAI,CAAC,SAAS;gBACxC,WAAW;gBACX,SAAS;gBACT,WAAW;YACb;QACF;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAElC,OAAO;YACL,SAAS,OAAO,UAAU,KAAK;YAC/B,UAAU,OAAO,UAAU,KAAK,KAAK,OAAO,iBAAiB,KAAK;YAClE,QAAQ,OAAO,MAAM;YACrB,eAAe,OAAO,OAAO;YAC7B,SAAS,OAAO,OAAO;QACzB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;YAAE,SAAS;YAAO,OAAO;QAAsB;IACxD;AACF;AAEA;;CAEC,GACD,eAAe,oBAAoB,OAAO,EAAE,SAAS;IACnD,+CAA+C;IAC/C,gDAAgD;IAChD,+BAA+B;IAC/B,MAAM,UAAU,IAAI;IACpB,MAAM,UAAU,QAAQ,MAAM,CAAC;IAC/B,MAAM,cAAc,QAAQ,MAAM,CAAC;IAEnC,MAAM,YAAY,MAAM,OAAO,MAAM,CAAC,SAAS,CAC7C,OACA,SACA;QAAE,MAAM;QAAQ,MAAM;IAAU,GAChC,OACA;QAAC;KAAO;IAGV,MAAM,YAAY,MAAM,OAAO,MAAM,CAAC,IAAI,CACxC,QACA,WACA;IAGF,wBAAwB;IACxB,OAAO,MAAM,IAAI,CAAC,IAAI,WAAW,YAC9B,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG,MACpC,IAAI,CAAC;AACV", "debugId": null}}]}