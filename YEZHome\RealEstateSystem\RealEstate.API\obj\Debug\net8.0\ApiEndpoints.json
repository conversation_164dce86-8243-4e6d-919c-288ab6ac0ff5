[{"ContainingType": "RealEstate.API.Controllers.AddressController", "Method": "GetCitiesAsync", "RelativePath": "api/Address/cities", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[RealEstate.Domain.Entities.City, RealEstate.Domain, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.API.Controllers.AddressController", "Method": "GetDistrictByCityAsync", "RelativePath": "api/Address/cities/{cityId}/districts", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "cityId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[RealEstate.Domain.Entities.District, RealEstate.Domain, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.API.Controllers.AddressController", "Method": "GetStreetByDistrictAsync", "RelativePath": "api/Address/districts/{districtId}/streets", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "districtId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[RealEstate.Domain.Entities.Street, RealEstate.Domain, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.API.Controllers.AddressController", "Method": "GetWardByDistrictAsync", "RelativePath": "api/Address/districts/{districtId}/wards", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "districtId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[RealEstate.Domain.Entities.Ward, RealEstate.Domain, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.API.Controllers.AddressController", "Method": "GetProjectsByWardStreetAsync", "RelativePath": "api/Address/wards/{wardId}/streets/{streetId}/projects", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "wardId", "Type": "System.Int32", "IsRequired": true}, {"Name": "streetId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[RealEstate.Domain.Entities.Project, RealEstate.Domain, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.API.Controllers.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/Auth/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "loginDto", "Type": "RealEstate.Application.DTO.LoginDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.UserDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.API.Controllers.AuthController", "Method": "GetUserProfile", "RelativePath": "api/Auth/me", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.ProfileDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.API.Controllers.AuthController", "Method": "Password", "RelativePath": "api/Auth/me/password", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "changePasswordDto", "Type": "RealEstate.Application.DTO.ChangePasswordDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.UserDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.API.Controllers.AuthController", "Method": "RefreshToken", "RelativePath": "api/Auth/refresh-token", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "RealEstate.API.Controllers.AuthController", "Method": "Register", "RelativePath": "api/Auth/register", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "registerDto", "Type": "RealEstate.Application.DTO.CreateUserDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.UserDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.API.Controllers.AuthController", "Method": "ResetPassword", "RelativePath": "api/Auth/reset-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "resetPasswordDto", "Type": "RealEstate.Application.DTO.ForgotPasswordDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.UserDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.API.Controllers.AuthController", "Method": "ValidateToken", "RelativePath": "api/Auth/validate-token", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "RealEstate.API.Controllers.BlogController", "Method": "GetAllBlogPost", "RelativePath": "api/Blog", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[RealEstate.Application.DTO.BlogPostDto, RealEstate.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.API.Controllers.BlogController", "Method": "GetBlogPosts", "RelativePath": "api/Blog/blog-posts", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "SortColumn", "Type": "System.String", "IsRequired": false}, {"Name": "SortDescending", "Type": "System.Boolean", "IsRequired": false}, {"Name": "title", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "RealEstate.API.Controllers.BlogController", "Method": "GetBlogPostBySlug", "RelativePath": "api/Blog/slug/{slug}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "slug", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.BlogPostDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.API.Controllers.ContactRequestController", "Method": "GetAllContactRequests", "RelativePath": "api/ContactRequest", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[RealEstate.Application.DTO.ContactRequestDto, RealEstate.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.API.Controllers.ContactRequestController", "Method": "CreateContactRequest", "RelativePath": "api/ContactRequest", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "requestDto", "Type": "RealEstate.Application.DTO.CreateContactRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.ContactRequestDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.API.Controllers.ContactRequestController", "Method": "GetContactRequestById", "RelativePath": "api/ContactRequest/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.ContactRequestDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.API.Controllers.ContactRequestController", "Method": "UpdateContactRequest", "RelativePath": "api/ContactRequest/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}, {"Name": "requestDto", "Type": "RealEstate.Application.DTO.UpdateContactRequestDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "RealEstate.API.Controllers.ContactRequestController", "Method": "DeleteContactRequest", "RelativePath": "api/ContactRequest/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "RealEstate.API.Controllers.ContactRequestController", "Method": "GetContactRequestsByPropertyId", "RelativePath": "api/ContactRequest/property/{propertyId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "propertyId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[RealEstate.Application.DTO.ContactRequestDto, RealEstate.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.API.Controllers.ContactRequestController", "Method": "GetContactRequests", "RelativePath": "api/ContactRequest/requests", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "SortColumn", "Type": "System.String", "IsRequired": false}, {"Name": "SortDescending", "Type": "System.Boolean", "IsRequired": false}, {"Name": "phone", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "RealEstate.API.Controllers.PropertyAnalyticsController", "Method": "LogPropertyEvent", "RelativePath": "api/log/property-event", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "RealEstate.API.DTO.LogPropertyEventRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "RealEstate.API.Controllers.PropertyAnalyticsController", "Method": "LogPropertyViewFromFrontend", "RelativePath": "api/log/property-view", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "RealEstate.API.DTO.LogPropertyViewRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "RealEstate.API.Controllers.NotificationPreferenceController", "Method": "GetPreferences", "RelativePath": "api/notification-preferences", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "RealEstate.API.Controllers.NotificationPreferenceController", "Method": "UpdatePreferences", "RelativePath": "api/notification-preferences", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "preferencesDto", "Type": "RealEstate.Application.DTO.NotificationPreferenceDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "RealEstate.API.Controllers.NotificationController", "Method": "GetAll", "RelativePath": "api/notifications", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "fromDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "toDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.PagedResultDto`1[[RealEstate.Application.DTO.Notification.NotificationDto, RealEstate.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "RealEstate.API.Controllers.NotificationController", "Method": "GetById", "RelativePath": "api/notifications/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.Notification.NotificationDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "RealEstate.API.Controllers.NotificationController", "Method": "Delete", "RelativePath": "api/notifications/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "RealEstate.API.Controllers.NotificationController", "Method": "MarkAsRead", "RelativePath": "api/notifications/{id}/mark-as-read", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "RealEstate.API.Controllers.NotificationController", "Method": "GetByType", "RelativePath": "api/notifications/by-type/{type}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "type", "Type": "System.String", "IsRequired": true}, {"Name": "fromDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "toDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.PagedResultDto`1[[RealEstate.Application.DTO.Notification.NotificationDto, RealEstate.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "RealEstate.API.Controllers.NotificationController", "Method": "GetUserNotifications", "RelativePath": "api/notifications/for-user", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "fromDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "toDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.PagedResultDto`1[[RealEstate.Application.DTO.Notification.NotificationDto, RealEstate.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "RealEstate.API.Controllers.NotificationController", "Method": "MarkAllAsRead", "RelativePath": "api/notifications/mark-all-as-read", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "RealEstate.API.Controllers.NotificationController", "Method": "MarkMultipleAsRead", "RelativePath": "api/notifications/mark-multiple-as-read", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "notificationIds", "Type": "System.Collections.Generic.List`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "RealEstate.API.Controllers.NotificationController", "Method": "GetUnreadCount", "RelativePath": "api/notifications/unread-count", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.Notification.UnreadNotificationCountDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "RealEstate.API.Controllers.PropertyController", "Method": "GetAllProperties", "RelativePath": "api/Property", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[RealEstate.Application.DTO.PropertyDto, RealEstate.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.API.Controllers.PropertyController", "Method": "CreateProperty", "RelativePath": "api/Property", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "propertyDto", "Type": "RealEstate.Application.DTO.CreatePropertyDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.PropertyDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}]}, {"ContainingType": "RealEstate.API.Controllers.PropertyController", "Method": "GetPropertyById", "RelativePath": "api/Property/{propertyId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "propertyId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.PropertyDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "RealEstate.API.Controllers.PropertyController", "Method": "UpdateProperty", "RelativePath": "api/Property/{propertyId}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "propertyId", "Type": "System.Guid", "IsRequired": true}, {"Name": "propertyDto", "Type": "RealEstate.Application.DTO.CreatePropertyDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 201}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "RealEstate.API.Controllers.PropertyController", "Method": "DeleteProperty", "RelativePath": "api/Property/{propertyId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "propertyId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "RealEstate.API.Controllers.PropertyController", "Method": "UpdateHighlight", "RelativePath": "api/Property/{propertyId}/highlight", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "propertyId", "Type": "System.Guid", "IsRequired": true}, {"Name": "request", "Type": "RealEstate.Application.DTO.UpdateHighlightDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.PropertyDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "RealEstate.API.Controllers.PropertyController", "Method": "UpdateStatus", "RelativePath": "api/Property/{propertyId}/status", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "propertyId", "Type": "System.Guid", "IsRequired": true}, {"Name": "request", "Type": "RealEstate.Application.DTO.UpdateStatusDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "RealEstate.API.Controllers.PropertyController", "Method": "DeleteProperties", "RelativePath": "api/Property/bulk", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "RealEstate.Application.DTO.BulkPropertyIdsDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}]}, {"ContainingType": "RealEstate.API.Controllers.PropertyController", "Method": "UpdateHighlightBulk", "RelativePath": "api/Property/bulk/highlight", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "RealEstate.Application.DTO.BulkUpdateHighlightDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}]}, {"ContainingType": "RealEstate.API.Controllers.PropertyController", "Method": "UpdateStatusBulk", "RelativePath": "api/Property/bulk/status", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "RealEstate.Application.DTO.BulkUpdateStatusDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}]}, {"ContainingType": "RealEstate.API.Controllers.PropertyController", "Method": "VerifyPropertyRemainingTimes", "RelativePath": "api/Property/edit-remaining/{propertyId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "propertyId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[RealEstate.Application.DTO.PropertyDto, RealEstate.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}]}, {"ContainingType": "RealEstate.API.Controllers.PropertyController", "Method": "GetAllPropertiesByUser", "RelativePath": "api/Property/me", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "status", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.PagedResultDto`1[[RealEstate.Application.DTO.PropertyDto, RealEstate.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}]}, {"ContainingType": "RealEstate.API.Controllers.PropertyController", "Method": "GetNearbyProperties", "RelativePath": "api/Property/nearby", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "swLat", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "swLng", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "neLat", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "neLng", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.PagedResultDto`1[[RealEstate.Application.DTO.PropertyDto, RealEstate.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "RealEstate.API.Controllers.PropertyController", "Method": "RenewProperty", "RelativePath": "api/Property/renew", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "RealEstate.Application.DTO.PropertyRenewalDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.PropertyDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 403}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "RealEstate.API.Controllers.PropertyController", "Method": "SearchProperties", "RelativePath": "api/Property/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "postType", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "propertyType", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "cityId", "Type": "System.String", "IsRequired": false}, {"Name": "districtId", "Type": "System.String", "IsRequired": false}, {"Name": "address", "Type": "System.String", "IsRequired": false}, {"Name": "minPrice", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "maxPrice", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "minArea", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "maxArea", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "minRooms", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "minToilets", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "direction", "Type": "System.String", "IsRequired": false}, {"Name": "legality", "Type": "System.String", "IsRequired": false}, {"Name": "minRoadWidth", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "swLat", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "swLng", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "neLat", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "neLng", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.PagedResultDto`1[[RealEstate.Application.DTO.PropertyDto, RealEstate.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 500}]}, {"ContainingType": "RealEstate.API.Controllers.PropertyController", "Method": "GetPropertyCountStats", "RelativePath": "api/Property/stats", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.PropertyCountStatsDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}]}, {"ContainingType": "RealEstate.API.Controllers.PropertyController", "Method": "UploadPropertyImages", "RelativePath": "api/Property/upload-images", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "propertyId", "Type": "System.Nullable`1[[System.Guid, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "files", "Type": "Microsoft.AspNetCore.Http.IFormFileCollection", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[RealEstate.Application.DTO.PropertyMediaDto, RealEstate.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 400}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "RealEstate.API.Controllers.PropertyAnalyticsController", "Method": "GetPropertyHistoryStatus", "RelativePath": "api/PropertyAnalytics/history/status/{propertyId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "propertyId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[RealEstate.Application.DTO.PropertyStatusLogDto, RealEstate.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 401}]}, {"ContainingType": "RealEstate.API.Controllers.PropertyAnalyticsController", "Method": "GetPropertyAnalytics", "RelativePath": "api/PropertyAnalytics/property/{propertyId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "propertyId", "Type": "System.Guid", "IsRequired": true}, {"Name": "startDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.Analytics.PropertyAnalyticsDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.API.Controllers.PropertyAnalyticsController", "Method": "ExportPropertyAnalytics", "RelativePath": "api/PropertyAnalytics/property/{propertyId}/export", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "propertyId", "Type": "System.Guid", "IsRequired": true}, {"Name": "startDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "RealEstate.API.Controllers.PropertyAnalyticsController", "Method": "GetPropertyEngagementSummary", "RelativePath": "api/PropertyAnalytics/property/{propertyId}/summary", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "propertyId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "RealEstate.API.Controllers.PropertyAnalyticsController", "Method": "LogPropertyView", "RelativePath": "api/PropertyAnalytics/property/{propertyId}/view", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "propertyId", "Type": "System.Guid", "IsRequired": true}, {"Name": "request", "Type": "RealEstate.API.DTO.LogPropertyViewRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "RealEstate.API.Controllers.PropertyAnalyticsController", "Method": "LogPropertySpending", "RelativePath": "api/PropertyAnalytics/spending", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "RealEstate.API.DTO.LogPropertySpendingRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "RealEstate.API.Controllers.PropertyAnalyticsController", "Method": "GetUserPropertiesAnalytics", "RelativePath": "api/PropertyAnalytics/user", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "StartDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PropertyStatuses", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SpendingTypes", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "MinSpent", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "MaxSpent", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "MinViews", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "MaxViews", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SortBy", "Type": "System.String", "IsRequired": false}, {"Name": "SortDescending", "Type": "System.Boolean", "IsRequired": false}, {"Name": "Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.PagedResultDto`1[[RealEstate.Application.DTO.Analytics.PropertyAnalyticsDto, RealEstate.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.API.Controllers.PropertyAnalyticsController", "Method": "ExportUserPropertiesAnalytics", "RelativePath": "api/PropertyAnalytics/user/export", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "StartDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "EndDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PropertyStatuses", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SpendingTypes", "Type": "System.Collections.Generic.List`1[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "MinSpent", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "MaxSpent", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "MinViews", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "MaxViews", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SortBy", "Type": "System.String", "IsRequired": false}, {"Name": "SortDescending", "Type": "System.Boolean", "IsRequired": false}, {"Name": "Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "RealEstate.API.Controllers.UserController", "Method": "GetUser", "RelativePath": "api/User/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "RealEstate.API.Controllers.UserController", "Method": "GetUserDashboard", "RelativePath": "api/User/dashboard", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.UserDashboardDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.API.Controllers.UserController", "Method": "DeactivateAccount", "RelativePath": "api/User/deactivate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "deactivateUserDto", "Type": "RealEstate.Application.DTO.DeactivateUserDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "RealEstate.API.Controllers.UserController", "Method": "PermanentDeleteAccount", "RelativePath": "api/User/permanent-delete", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "deactivateUserDto", "Type": "RealEstate.Application.DTO.DeactivateUserDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "RealEstate.API.Controllers.UserController", "Method": "GetPropertyPerformance", "RelativePath": "api/User/properties/performance", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "RealEstate.API.Controllers.UserController", "Method": "GetUserPropertyStats", "RelativePath": "api/User/properties/stats", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.PropertyStatsDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.API.Controllers.UserController", "Method": "GetUserRanking", "RelativePath": "api/User/ranking", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.MemberRankingDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.API.Controllers.UserController", "Method": "AddUserRole", "RelativePath": "api/User/role", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "addUserRoleDto", "Type": "RealEstate.Application.DTO.AddUserRoleDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.AddUserRoleDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.API.Controllers.UserController", "Method": "GetMonthlySpending", "RelativePath": "api/User/spending/monthly", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "year", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "RealEstate.API.Controllers.UserController", "Method": "GetUserTaxInfo", "RelativePath": "api/User/tax-info", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.UserInvoiceInfoDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.API.Controllers.UserController", "Method": "UpdateUserTaxInfo", "RelativePath": "api/User/tax-info", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "taxInfoDto", "Type": "RealEstate.Application.DTO.UpdateUserTaxInfoDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "RealEstate.API.Controllers.UserController", "Method": "GetUserTransactions", "RelativePath": "api/User/transactions", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "count", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[RealEstate.Application.DTO.WalletTransactionDto, RealEstate.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.API.Controllers.UserController", "Method": "GetUserWallet", "RelativePath": "api/User/wallet", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.WalletInfoDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.API.Controllers.UserAvatarController", "Method": "GetAvatarImage", "RelativePath": "api/UserAvatar", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.UserAvatarDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.API.Controllers.UserAvatarController", "Method": "RemoveAvatar", "RelativePath": "api/UserAvatar", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "RealEstate.API.Controllers.UserAvatarController", "Method": "UploadAvatar", "RelativePath": "api/UserAvatar/upload", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.UserDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.API.Controllers.UserFavoritesController", "Method": "AddToFavorites", "RelativePath": "api/UserFavorites/add", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "RealEstate.Application.DTO.UserFavorite.CreateUserFavoriteDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "RealEstate.API.Controllers.UserFavoritesController", "Method": "CheckFavoriteStatus", "RelativePath": "api/UserFavorites/check", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "RealEstate.Application.DTO.UserFavorite.FavoriteCheckRequestDto", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[RealEstate.Application.DTO.UserFavorite.FavoriteStatusDto, RealEstate.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.API.Controllers.UserFavoritesController", "Method": "GetFavoritesCount", "RelativePath": "api/UserFavorites/count", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.UserFavorite.FavoriteCountDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.API.Controllers.UserFavoritesController", "Method": "GetUserFavorites", "RelativePath": "api/UserFavorites/favorites", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[RealEstate.Application.DTO.UserFavoriteDto, RealEstate.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.API.Controllers.UserFavoritesController", "Method": "GetUserFavoritesWithDetails", "RelativePath": "api/UserFavorites/favorites-with-details", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "minPrice", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "maxPrice", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "fromDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "toDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "sortBy", "Type": "System.String", "IsRequired": false}, {"Name": "sortDescending", "Type": "System.Boolean", "IsRequired": false}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.UserFavorite.PagedFavoriteResultDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.API.Controllers.UserFavoritesController", "Method": "RemoveFromFavorites", "RelativePath": "api/UserFavorites/remove/{propertyId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "propertyId", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "RealEstate.API.Controllers.WalletTransactionController", "Method": "GetTransactions", "RelativePath": "api/WalletTransaction", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[RealEstate.Application.DTO.WalletTransactionDto, RealEstate.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.API.Controllers.WalletTransactionController", "Method": "GetTransactionById", "RelativePath": "api/WalletTransaction/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.WalletTransactionDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.API.Controllers.WalletTransactionController", "Method": "GetBalance", "RelativePath": "api/WalletTransaction/balance", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.Wallet.WalletBalanceDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.API.Controllers.WalletTransactionController", "Method": "ExportTransactions", "RelativePath": "api/WalletTransaction/export", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "startDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "status", "Type": "System.String", "IsRequired": false}, {"Name": "paymentMethod", "Type": "System.String", "IsRequired": false}, {"Name": "type", "Type": "System.String", "IsRequired": false}, {"Name": "minAmount", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "maxAmount", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "searchTerm", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "RealEstate.API.Controllers.WalletTransactionController", "Method": "GetUserPendingTransactions", "RelativePath": "api/WalletTransaction/pending", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[RealEstate.Application.DTO.WalletTransactionDto, RealEstate.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.API.Controllers.WalletTransactionController", "Method": "SearchTransactions", "RelativePath": "api/WalletTransaction/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "startDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "status", "Type": "System.String", "IsRequired": false}, {"Name": "paymentMethod", "Type": "System.String", "IsRequired": false}, {"Name": "type", "Type": "System.String", "IsRequired": false}, {"Name": "minAmount", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "maxAmount", "Type": "System.Nullable`1[[System.Decimal, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "searchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "page", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.TransactionSearchResultDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.API.Controllers.WalletTransactionController", "Method": "SpendFromWallet", "RelativePath": "api/WalletTransaction/spend", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "RealEstate.Application.DTO.Wallet.SpendWalletDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.WalletTransactionDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.API.Controllers.WalletTransactionController", "Method": "TopUpWallet", "RelativePath": "api/WalletTransaction/topup", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "RealEstate.Application.DTO.Wallet.TopUpWalletDto", "IsRequired": true}], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.WalletTransactionDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.API.Controllers.YezDataController", "Method": "GetHighlightFees", "RelativePath": "api/YezData/highlight-fees", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[RealEstate.Application.DTO.HighlightFeeDto, RealEstate.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.API.Controllers.YezDataController", "Method": "GetHighlightFeeByRank", "RelativePath": "api/YezData/highlight-fees/{rankName}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "rankName", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.HighlightFeeDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "RealEstate.API.Controllers.YezDataController", "Method": "GetMemberRankings", "RelativePath": "api/YezData/member-rankings", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[RealEstate.Application.DTO.MemberRankListDto, RealEstate.Application, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "RealEstate.API.Controllers.YezDataController", "Method": "GetMemberRankingByName", "RelativePath": "api/YezData/member-rankings/{rankName}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "rankName", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "RealEstate.Application.DTO.MemberRankListDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}, {"Type": "Microsoft.AspNetCore.Mvc.ProblemDetails", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 404}]}, {"ContainingType": "RealEstate.API.Controllers.MediaController", "Method": "GetMedia", "RelativePath": "Media/{fileId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "fileId", "Type": "System.Guid", "IsRequired": true}, {"Name": "size", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "RealEstate.API.Controllers.MediaController", "Method": "DeleteMedia", "RelativePath": "Media/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Guid", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "RealEstate.API.Controllers.MediaController", "Method": "UpdateCaption", "RelativePath": "Media/update-caption", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "updateDto", "Type": "RealEstate.Application.DTO.UpdatePropertyMediaCaptionDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "RealEstate.API.Controllers.MediaController", "Method": "UpdateIsAvatar", "RelativePath": "Media/update-is-avatar", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "updateDto", "Type": "RealEstate.Application.DTO.UpdatePropertyMediaIsAvatarDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "RealEstate.API.Controllers.UserAvatarMediaController", "Method": "GetMedia", "RelativePath": "UserAvatarMedia/{fileId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "fileId", "Type": "System.Guid", "IsRequired": true}, {"Name": "size", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}]