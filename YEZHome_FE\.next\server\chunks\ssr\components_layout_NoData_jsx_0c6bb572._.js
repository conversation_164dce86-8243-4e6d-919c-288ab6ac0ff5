module.exports = {

"[project]/components/layout/NoData.jsx [app-ssr] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/_3f5b37ae._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/components/layout/NoData.jsx [app-ssr] (ecmascript, next/dynamic entry)");
    });
});
}}),

};