using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;

namespace RealEstate.API.Controllers
{
    /// <summary>
    /// Controller for managing invoices and invoice-related operations
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class InvoiceController : BaseController
    {
        private readonly IInvoiceService _invoiceService;
        private readonly ILogger<InvoiceController> _logger;

        public InvoiceController(
            IInvoiceService invoiceService,
            ILogger<InvoiceController> logger)
        {
            _invoiceService = invoiceService;
            _logger = logger;
        }

        #region Basic Invoice Operations

        /// <summary>
        /// Get invoice by ID
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<InvoiceDto>> GetInvoiceById(Guid id)
        {
            try
            {
                LogUserAction(_logger, "GetInvoiceById", new { InvoiceId = id });
                
                var invoice = await _invoiceService.GetInvoiceByIdAsync(id);
                return Ok(invoice);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting invoice {InvoiceId}", id);
                return StatusCode(500, "An error occurred while retrieving the invoice");
            }
        }

        /// <summary>
        /// Create new invoice
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<InvoiceDto>> CreateInvoice([FromBody] CreateInvoiceDto dto)
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                    return Unauthorized("User not authenticated");

                LogUserAction(_logger, "CreateInvoice", new { PropertyId = dto.PropertyId, Type = dto.Type, Amount = dto.TotalAmount });

                var invoice = await _invoiceService.CreateInvoiceAsync(dto, userId.Value);
                return CreatedAtAction(nameof(GetInvoiceById), new { id = invoice.Id }, invoice);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating invoice for property {PropertyId}", dto.PropertyId);
                return StatusCode(500, "An error occurred while creating the invoice");
            }
        }

        /// <summary>
        /// Update invoice
        /// </summary>
        [HttpPut("{id}")]
        public async Task<ActionResult<InvoiceDto>> UpdateInvoice(Guid id, [FromBody] UpdateInvoiceDto dto)
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                    return Unauthorized("User not authenticated");

                LogUserAction(_logger, "UpdateInvoice", new { InvoiceId = id, Updates = dto });

                var invoice = await _invoiceService.UpdateInvoiceAsync(id, dto, userId.Value);
                return Ok(invoice);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (UnauthorizedAccessException ex)
            {
                return Forbid(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating invoice {InvoiceId}", id);
                return StatusCode(500, "An error occurred while updating the invoice");
            }
        }

        /// <summary>
        /// Delete invoice
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteInvoice(Guid id)
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                    return Unauthorized("User not authenticated");

                LogUserAction(_logger, "DeleteInvoice", new { InvoiceId = id });

                var result = await _invoiceService.DeleteInvoiceAsync(id, userId.Value);
                if (!result)
                    return NotFound("Invoice not found");

                return NoContent();
            }
            catch (UnauthorizedAccessException ex)
            {
                return Forbid(ex.Message);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting invoice {InvoiceId}", id);
                return StatusCode(500, "An error occurred while deleting the invoice");
            }
        }

        #endregion

        #region User-Based Endpoints

        /// <summary>
        /// Get user's invoices with filtering and pagination
        /// </summary>
        [HttpGet("user/{userId}")]
        public async Task<ActionResult<PagedResultDto<InvoiceDto>>> GetUserInvoices(Guid userId, [FromQuery] InvoiceFilterDto filter)
        {
            try
            {
                var currentUserId = GetUserId();
                if (!currentUserId.HasValue)
                    return Unauthorized("User not authenticated");

                // Users can only access their own invoices unless they're admin
                if (currentUserId.Value != userId && !User.IsInRole("Admin"))
                    return Forbid("You can only access your own invoices");

                LogUserAction(_logger, "GetUserInvoices", new { UserId = userId, Filter = filter });

                var invoices = await _invoiceService.GetInvoicesByUserAsync(userId, filter);
                return Ok(invoices);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting invoices for user {UserId}", userId);
                return StatusCode(500, "An error occurred while retrieving user invoices");
            }
        }

        /// <summary>
        /// Get user's invoices by status
        /// </summary>
        [HttpGet("user/{userId}/status/{status}")]
        public async Task<ActionResult<IEnumerable<InvoiceDto>>> GetUserInvoicesByStatus(Guid userId, string status)
        {
            try
            {
                var currentUserId = GetUserId();
                if (!currentUserId.HasValue)
                    return Unauthorized("User not authenticated");

                if (currentUserId.Value != userId && !User.IsInRole("Admin"))
                    return Forbid("You can only access your own invoices");

                LogUserAction(_logger, "GetUserInvoicesByStatus", new { UserId = userId, Status = status });

                var invoices = await _invoiceService.GetUserInvoicesByStatusAsync(userId, status);
                return Ok(invoices);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting invoices by status for user {UserId}", userId);
                return StatusCode(500, "An error occurred while retrieving user invoices by status");
            }
        }

        /// <summary>
        /// Get user's invoices by type
        /// </summary>
        [HttpGet("user/{userId}/type/{type}")]
        public async Task<ActionResult<IEnumerable<InvoiceDto>>> GetUserInvoicesByType(Guid userId, string type)
        {
            try
            {
                var currentUserId = GetUserId();
                if (!currentUserId.HasValue)
                    return Unauthorized("User not authenticated");

                if (currentUserId.Value != userId && !User.IsInRole("Admin"))
                    return Forbid("You can only access your own invoices");

                LogUserAction(_logger, "GetUserInvoicesByType", new { UserId = userId, Type = type });

                var invoices = await _invoiceService.GetUserInvoicesByTypeAsync(userId, type);
                return Ok(invoices);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting invoices by type for user {UserId}", userId);
                return StatusCode(500, "An error occurred while retrieving user invoices by type");
            }
        }

        /// <summary>
        /// Get user's invoice statistics
        /// </summary>
        [HttpGet("user/{userId}/stats")]
        public async Task<ActionResult<InvoiceStatsDto>> GetUserInvoiceStats(Guid userId)
        {
            try
            {
                var currentUserId = GetUserId();
                if (!currentUserId.HasValue)
                    return Unauthorized("User not authenticated");

                if (currentUserId.Value != userId && !User.IsInRole("Admin"))
                    return Forbid("You can only access your own invoice statistics");

                LogUserAction(_logger, "GetUserInvoiceStats", new { UserId = userId });

                var stats = await _invoiceService.GetUserInvoiceStatsAsync(userId);
                return Ok(stats);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting invoice stats for user {UserId}", userId);
                return StatusCode(500, "An error occurred while retrieving user invoice statistics");
            }
        }

        /// <summary>
        /// Get user's invoice statistics by date range
        /// </summary>
        [HttpGet("user/{userId}/stats/daterange")]
        public async Task<ActionResult<InvoiceStatsDto>> GetUserInvoiceStatsByDateRange(
            Guid userId, 
            [FromQuery] DateTime startDate, 
            [FromQuery] DateTime endDate)
        {
            try
            {
                var currentUserId = GetUserId();
                if (!currentUserId.HasValue)
                    return Unauthorized("User not authenticated");

                if (currentUserId.Value != userId && !User.IsInRole("Admin"))
                    return Forbid("You can only access your own invoice statistics");

                LogUserAction(_logger, "GetUserInvoiceStatsByDateRange", new { UserId = userId, StartDate = startDate, EndDate = endDate });

                var stats = await _invoiceService.GetUserInvoiceStatsByDateRangeAsync(userId, startDate, endDate);
                return Ok(stats);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting invoice stats by date range for user {UserId}", userId);
                return StatusCode(500, "An error occurred while retrieving user invoice statistics");
            }
        }

        #endregion

        #region Property-Based Endpoints

        /// <summary>
        /// Get property's invoices with filtering and pagination
        /// </summary>
        [HttpGet("property/{propertyId}")]
        public async Task<ActionResult<PagedResultDto<InvoiceDto>>> GetPropertyInvoices(Guid propertyId, [FromQuery] InvoiceFilterDto filter)
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                    return Unauthorized("User not authenticated");

                LogUserAction(_logger, "GetPropertyInvoices", new { PropertyId = propertyId, Filter = filter });

                var invoices = await _invoiceService.GetInvoicesByPropertyAsync(propertyId, filter);
                return Ok(invoices);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting invoices for property {PropertyId}", propertyId);
                return StatusCode(500, "An error occurred while retrieving property invoices");
            }
        }

        /// <summary>
        /// Get property's invoices by status
        /// </summary>
        [HttpGet("property/{propertyId}/status/{status}")]
        public async Task<ActionResult<IEnumerable<InvoiceDto>>> GetPropertyInvoicesByStatus(Guid propertyId, string status)
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                    return Unauthorized("User not authenticated");

                LogUserAction(_logger, "GetPropertyInvoicesByStatus", new { PropertyId = propertyId, Status = status });

                var invoices = await _invoiceService.GetPropertyInvoicesByStatusAsync(propertyId, status);
                return Ok(invoices);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting invoices by status for property {PropertyId}", propertyId);
                return StatusCode(500, "An error occurred while retrieving property invoices by status");
            }
        }

        /// <summary>
        /// Get property's invoices by type
        /// </summary>
        [HttpGet("property/{propertyId}/type/{type}")]
        public async Task<ActionResult<IEnumerable<InvoiceDto>>> GetPropertyInvoicesByType(Guid propertyId, string type)
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                    return Unauthorized("User not authenticated");

                LogUserAction(_logger, "GetPropertyInvoicesByType", new { PropertyId = propertyId, Type = type });

                var invoices = await _invoiceService.GetPropertyInvoicesByTypeAsync(propertyId, type);
                return Ok(invoices);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting invoices by type for property {PropertyId}", propertyId);
                return StatusCode(500, "An error occurred while retrieving property invoices by type");
            }
        }

        /// <summary>
        /// Get property's invoice statistics
        /// </summary>
        [HttpGet("property/{propertyId}/stats")]
        public async Task<ActionResult<PropertyInvoiceStatsDto>> GetPropertyInvoiceStats(Guid propertyId)
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                    return Unauthorized("User not authenticated");

                LogUserAction(_logger, "GetPropertyInvoiceStats", new { PropertyId = propertyId });

                var stats = await _invoiceService.GetPropertyInvoiceStatsAsync(propertyId);
                return Ok(stats);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting invoice stats for property {PropertyId}", propertyId);
                return StatusCode(500, "An error occurred while retrieving property invoice statistics");
            }
        }

        /// <summary>
        /// Get property's invoice statistics by date range
        /// </summary>
        [HttpGet("property/{propertyId}/stats/daterange")]
        public async Task<ActionResult<PropertyInvoiceStatsDto>> GetPropertyInvoiceStatsByDateRange(
            Guid propertyId,
            [FromQuery] DateTime startDate,
            [FromQuery] DateTime endDate)
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                    return Unauthorized("User not authenticated");

                LogUserAction(_logger, "GetPropertyInvoiceStatsByDateRange", new { PropertyId = propertyId, StartDate = startDate, EndDate = endDate });

                var stats = await _invoiceService.GetPropertyInvoiceStatsByDateRangeAsync(propertyId, startDate, endDate);
                return Ok(stats);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting invoice stats by date range for property {PropertyId}", propertyId);
                return StatusCode(500, "An error occurred while retrieving property invoice statistics");
            }
        }

        /// <summary>
        /// Get total amount spent on property
        /// </summary>
        [HttpGet("property/{propertyId}/total-spent")]
        public async Task<ActionResult<decimal>> GetPropertyTotalSpent(Guid propertyId)
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                    return Unauthorized("User not authenticated");

                LogUserAction(_logger, "GetPropertyTotalSpent", new { PropertyId = propertyId });

                var totalSpent = await _invoiceService.GetPropertyTotalSpentAsync(propertyId);
                return Ok(totalSpent);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting total spent for property {PropertyId}", propertyId);
                return StatusCode(500, "An error occurred while retrieving property total spent");
            }
        }

        /// <summary>
        /// Get property's invoice history
        /// </summary>
        [HttpGet("property/{propertyId}/history")]
        public async Task<ActionResult<IEnumerable<InvoiceDto>>> GetPropertyInvoiceHistory(Guid propertyId)
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                    return Unauthorized("User not authenticated");

                LogUserAction(_logger, "GetPropertyInvoiceHistory", new { PropertyId = propertyId });

                var invoices = await _invoiceService.GetPropertyInvoiceHistoryAsync(propertyId);
                return Ok(invoices);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting invoice history for property {PropertyId}", propertyId);
                return StatusCode(500, "An error occurred while retrieving property invoice history");
            }
        }

        #endregion

        #region Advanced Query Endpoints

        /// <summary>
        /// Get invoices by user and property
        /// </summary>
        [HttpGet("user/{userId}/property/{propertyId}")]
        public async Task<ActionResult<PagedResultDto<InvoiceDto>>> GetInvoicesByUserAndProperty(
            Guid userId,
            Guid propertyId,
            [FromQuery] InvoiceFilterDto filter)
        {
            try
            {
                var currentUserId = GetUserId();
                if (!currentUserId.HasValue)
                    return Unauthorized("User not authenticated");

                if (currentUserId.Value != userId && !User.IsInRole("Admin"))
                    return Forbid("You can only access your own invoices");

                LogUserAction(_logger, "GetInvoicesByUserAndProperty", new { UserId = userId, PropertyId = propertyId, Filter = filter });

                var invoices = await _invoiceService.GetInvoicesByUserAndPropertyAsync(userId, propertyId, filter);
                return Ok(invoices);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting invoices for user {UserId} and property {PropertyId}", userId, propertyId);
                return StatusCode(500, "An error occurred while retrieving invoices");
            }
        }

        /// <summary>
        /// Get pending invoices
        /// </summary>
        [HttpGet("pending")]
        public async Task<ActionResult<IEnumerable<InvoiceDto>>> GetPendingInvoices(
            [FromQuery] Guid? userId = null,
            [FromQuery] Guid? propertyId = null)
        {
            try
            {
                var currentUserId = GetUserId();
                if (!currentUserId.HasValue)
                    return Unauthorized("User not authenticated");

                // If userId is specified and it's not the current user, check admin role
                if (userId.HasValue && currentUserId.Value != userId.Value && !User.IsInRole("Admin"))
                    return Forbid("You can only access your own invoices");

                LogUserAction(_logger, "GetPendingInvoices", new { UserId = userId, PropertyId = propertyId });

                var invoices = await _invoiceService.GetPendingInvoicesAsync(userId, propertyId);
                return Ok(invoices);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting pending invoices");
                return StatusCode(500, "An error occurred while retrieving pending invoices");
            }
        }

        /// <summary>
        /// Get overdue invoices
        /// </summary>
        [HttpGet("overdue")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<IEnumerable<InvoiceDto>>> GetOverdueInvoices([FromQuery] int daysOverdue = 30)
        {
            try
            {
                LogUserAction(_logger, "GetOverdueInvoices", new { DaysOverdue = daysOverdue });

                var invoices = await _invoiceService.GetOverdueInvoicesAsync(daysOverdue);
                return Ok(invoices);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting overdue invoices");
                return StatusCode(500, "An error occurred while retrieving overdue invoices");
            }
        }

        /// <summary>
        /// Search invoices
        /// </summary>
        [HttpGet("search")]
        public async Task<ActionResult<PagedResultDto<InvoiceDto>>> SearchInvoices(
            [FromQuery] string searchTerm,
            [FromQuery] InvoiceFilterDto filter)
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                    return Unauthorized("User not authenticated");

                // Non-admin users can only search their own invoices
                if (!User.IsInRole("Admin"))
                    filter.UserId = userId.Value;

                LogUserAction(_logger, "SearchInvoices", new { SearchTerm = searchTerm, Filter = filter });

                var invoices = await _invoiceService.SearchInvoicesAsync(searchTerm, filter);
                return Ok(invoices);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching invoices with term {SearchTerm}", searchTerm);
                return StatusCode(500, "An error occurred while searching invoices");
            }
        }

        #endregion

        #region Status Management Endpoints

        /// <summary>
        /// Mark invoice as paid
        /// </summary>
        [HttpPost("{id}/mark-paid")]
        public async Task<ActionResult> MarkInvoiceAsPaid(Guid id)
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                    return Unauthorized("User not authenticated");

                LogUserAction(_logger, "MarkInvoiceAsPaid", new { InvoiceId = id });

                var result = await _invoiceService.MarkInvoiceAsPaidAsync(id, userId.Value);
                if (!result)
                    return NotFound("Invoice not found");

                return Ok();
            }
            catch (UnauthorizedAccessException ex)
            {
                return Forbid(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking invoice {InvoiceId} as paid", id);
                return StatusCode(500, "An error occurred while updating invoice status");
            }
        }

        /// <summary>
        /// Mark invoice as failed
        /// </summary>
        [HttpPost("{id}/mark-failed")]
        public async Task<ActionResult> MarkInvoiceAsFailed(Guid id, [FromBody] string reason)
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                    return Unauthorized("User not authenticated");

                LogUserAction(_logger, "MarkInvoiceAsFailed", new { InvoiceId = id, Reason = reason });

                var result = await _invoiceService.MarkInvoiceAsFailedAsync(id, userId.Value, reason);
                if (!result)
                    return NotFound("Invoice not found");

                return Ok();
            }
            catch (UnauthorizedAccessException ex)
            {
                return Forbid(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error marking invoice {InvoiceId} as failed", id);
                return StatusCode(500, "An error occurred while updating invoice status");
            }
        }

        /// <summary>
        /// Cancel invoice
        /// </summary>
        [HttpPost("{id}/cancel")]
        public async Task<ActionResult> CancelInvoice(Guid id, [FromBody] string reason)
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                    return Unauthorized("User not authenticated");

                LogUserAction(_logger, "CancelInvoice", new { InvoiceId = id, Reason = reason });

                var result = await _invoiceService.CancelInvoiceAsync(id, userId.Value, reason);
                if (!result)
                    return NotFound("Invoice not found");

                return Ok();
            }
            catch (UnauthorizedAccessException ex)
            {
                return Forbid(ex.Message);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cancelling invoice {InvoiceId}", id);
                return StatusCode(500, "An error occurred while cancelling invoice");
            }
        }

        /// <summary>
        /// Update invoice status
        /// </summary>
        [HttpPut("{id}/status")]
        public async Task<ActionResult> UpdateInvoiceStatus(Guid id, [FromBody] string status)
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                    return Unauthorized("User not authenticated");

                LogUserAction(_logger, "UpdateInvoiceStatus", new { InvoiceId = id, Status = status });

                var result = await _invoiceService.UpdateInvoiceStatusAsync(id, status, userId.Value);
                if (!result)
                    return NotFound("Invoice not found");

                return Ok();
            }
            catch (UnauthorizedAccessException ex)
            {
                return Forbid(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating status for invoice {InvoiceId}", id);
                return StatusCode(500, "An error occurred while updating invoice status");
            }
        }

        #endregion

        #region Invoice Items Endpoints

        /// <summary>
        /// Get invoice items
        /// </summary>
        [HttpGet("{id}/items")]
        public async Task<ActionResult<IEnumerable<InvoiceItemDto>>> GetInvoiceItems(Guid id)
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                    return Unauthorized("User not authenticated");

                LogUserAction(_logger, "GetInvoiceItems", new { InvoiceId = id });

                var items = await _invoiceService.GetInvoiceItemsAsync(id);
                return Ok(items);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting items for invoice {InvoiceId}", id);
                return StatusCode(500, "An error occurred while retrieving invoice items");
            }
        }

        /// <summary>
        /// Add invoice item
        /// </summary>
        [HttpPost("{id}/items")]
        public async Task<ActionResult<InvoiceItemDto>> AddInvoiceItem(Guid id, [FromBody] CreateInvoiceItemDto dto)
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                    return Unauthorized("User not authenticated");

                LogUserAction(_logger, "AddInvoiceItem", new { InvoiceId = id, Item = dto });

                var item = await _invoiceService.AddInvoiceItemAsync(id, dto, userId.Value);
                return CreatedAtAction(nameof(GetInvoiceItems), new { id }, item);
            }
            catch (KeyNotFoundException ex)
            {
                return NotFound(ex.Message);
            }
            catch (UnauthorizedAccessException ex)
            {
                return Forbid(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding item to invoice {InvoiceId}", id);
                return StatusCode(500, "An error occurred while adding invoice item");
            }
        }

        /// <summary>
        /// Update invoice item
        /// </summary>
        [HttpPut("items/{itemId}")]
        public async Task<ActionResult> UpdateInvoiceItem(Guid itemId, [FromBody] CreateInvoiceItemDto dto)
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                    return Unauthorized("User not authenticated");

                LogUserAction(_logger, "UpdateInvoiceItem", new { ItemId = itemId, Updates = dto });

                var result = await _invoiceService.UpdateInvoiceItemAsync(itemId, dto, userId.Value);
                if (!result)
                    return NotFound("Invoice item not found");

                return Ok();
            }
            catch (UnauthorizedAccessException ex)
            {
                return Forbid(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating invoice item {ItemId}", itemId);
                return StatusCode(500, "An error occurred while updating invoice item");
            }
        }

        /// <summary>
        /// Delete invoice item
        /// </summary>
        [HttpDelete("items/{itemId}")]
        public async Task<ActionResult> DeleteInvoiceItem(Guid itemId)
        {
            try
            {
                var userId = GetUserId();
                if (!userId.HasValue)
                    return Unauthorized("User not authenticated");

                LogUserAction(_logger, "DeleteInvoiceItem", new { ItemId = itemId });

                var result = await _invoiceService.DeleteInvoiceItemAsync(itemId, userId.Value);
                if (!result)
                    return NotFound("Invoice item not found");

                return NoContent();
            }
            catch (UnauthorizedAccessException ex)
            {
                return Forbid(ex.Message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting invoice item {ItemId}", itemId);
                return StatusCode(500, "An error occurred while deleting invoice item");
            }
        }

        #endregion
    }
}
