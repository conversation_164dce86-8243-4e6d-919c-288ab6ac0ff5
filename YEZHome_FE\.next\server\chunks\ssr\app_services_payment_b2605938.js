module.exports = {

"[project]/app/services/payment.js [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/app_services_payment_b6182dfc.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/app/services/payment.js [app-rsc] (ecmascript)");
    });
});
}}),

};