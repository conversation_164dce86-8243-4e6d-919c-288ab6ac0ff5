using AutoMapper;
using Microsoft.EntityFrameworkCore;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.Common;
using RealEstate.Domain.Entities;
using RealEstate.Domain.Interfaces;

namespace RealEstate.Application.Services
{
    public class InvoiceService : IInvoiceService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;

        public InvoiceService(IUnitOfWork unitOfWork, IMapper mapper)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
        }

        #region Core CRUD Operations

        public async Task<InvoiceDto> GetInvoiceByIdAsync(Guid id)
        {
            var invoice = await _unitOfWork.Invoices.GetQueryable()
                .Include(i => i.User)
                .Include(i => i.Property)
                .Include(i => i.InvoiceItems)
                .FirstOrDefaultAsync(i => i.Id == id);

            if (invoice == null)
                throw new KeyNotFoundException($"Invoice with ID {id} not found.");

            return MapToInvoiceDto(invoice);
        }

        public async Task<InvoiceDto> CreateInvoiceAsync(CreateInvoiceDto dto, Guid userId)
        {
            // Validate property exists and user has access
            var property = await _unitOfWork.Properties.GetByIdAsync(dto.PropertyId);
            if (property == null)
                throw new KeyNotFoundException($"Property with ID {dto.PropertyId} not found.");

            var invoice = new Invoice
            {
                UserId = userId,
                PropertyId = dto.PropertyId,
                Type = FunctionHelper.GetEnumDescription(dto.Type),
                TotalAmount = dto.TotalAmount,
                Status = FunctionHelper.GetEnumDescription(EnumValues.InvoiceStatus.PENDING),
                Note = dto.Note,
                CreatedAt = DateTime.UtcNow
            };

            await _unitOfWork.Invoices.AddAsync(invoice);
            await _unitOfWork.SaveChangesAsync();

            // Add invoice items if provided
            if (dto.InvoiceItems?.Any() == true)
            {
                foreach (var itemDto in dto.InvoiceItems)
                {
                    var item = new InvoiceItem
                    {
                        InvoiceId = invoice.Id,
                        ItemType = FunctionHelper.GetEnumDescription(itemDto.ItemType),
                        Description = itemDto.Description,
                        Amount = itemDto.Amount
                    };
                    await _unitOfWork.InvoiceItems.AddAsync(item);
                }
                await _unitOfWork.SaveChangesAsync();
            }

            return await GetInvoiceByIdAsync(invoice.Id);
        }

        public async Task<InvoiceDto> UpdateInvoiceAsync(Guid id, UpdateInvoiceDto dto, Guid userId)
        {
            var invoice = await _unitOfWork.Invoices.GetByIdAsync(id);
            if (invoice == null)
                throw new KeyNotFoundException($"Invoice with ID {id} not found.");

            // Check if user has permission to update this invoice
            if (invoice.UserId != userId)
                throw new UnauthorizedAccessException("You don't have permission to update this invoice.");

            // Update fields if provided
            if (dto.Type.HasValue)
                invoice.Type = FunctionHelper.GetEnumDescription(dto.Type.Value);
            
            if (dto.TotalAmount.HasValue)
                invoice.TotalAmount = dto.TotalAmount.Value;
            
            if (dto.Status.HasValue)
                invoice.Status = FunctionHelper.GetEnumDescription(dto.Status.Value);
            
            if (dto.Note != null)
                invoice.Note = dto.Note;

            _unitOfWork.Invoices.Update(invoice);
            await _unitOfWork.SaveChangesAsync();

            return await GetInvoiceByIdAsync(id);
        }

        public async Task<bool> DeleteInvoiceAsync(Guid id, Guid userId)
        {
            var invoice = await _unitOfWork.Invoices.GetByIdAsync(id);
            if (invoice == null)
                return false;

            // Check if user has permission to delete this invoice
            if (invoice.UserId != userId)
                throw new UnauthorizedAccessException("You don't have permission to delete this invoice.");

            // Only allow deletion of pending invoices
            if (invoice.Status != FunctionHelper.GetEnumDescription(EnumValues.InvoiceStatus.PENDING))
                throw new InvalidOperationException("Only pending invoices can be deleted.");

            _unitOfWork.Invoices.Remove(invoice);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        #endregion

        #region User-Based Retrieval

        public async Task<PagedResultDto<InvoiceDto>> GetInvoicesByUserAsync(Guid userId, InvoiceFilterDto filter)
        {
            filter.UserId = userId;
            return await GetFilteredInvoicesAsync(filter);
        }

        public async Task<IEnumerable<InvoiceDto>> GetUserInvoicesByStatusAsync(Guid userId, string status)
        {
            var invoices = await _unitOfWork.Invoices.GetQueryable()
                .Include(i => i.User)
                .Include(i => i.Property)
                .Include(i => i.InvoiceItems)
                .Where(i => i.UserId == userId && i.Status == status)
                .OrderByDescending(i => i.CreatedAt)
                .ToListAsync();

            return invoices.Select(MapToInvoiceDto);
        }

        public async Task<IEnumerable<InvoiceDto>> GetUserInvoicesByTypeAsync(Guid userId, string type)
        {
            var invoices = await _unitOfWork.Invoices.GetQueryable()
                .Include(i => i.User)
                .Include(i => i.Property)
                .Include(i => i.InvoiceItems)
                .Where(i => i.UserId == userId && i.Type == type)
                .OrderByDescending(i => i.CreatedAt)
                .ToListAsync();

            return invoices.Select(MapToInvoiceDto);
        }

        public async Task<InvoiceStatsDto> GetUserInvoiceStatsAsync(Guid userId)
        {
            return await GetUserInvoiceStatsByDateRangeAsync(userId, DateTime.MinValue, DateTime.MaxValue);
        }

        public async Task<InvoiceStatsDto> GetUserInvoiceStatsByDateRangeAsync(Guid userId, DateTime startDate, DateTime endDate)
        {
            var user = await _unitOfWork.AppUsers.GetByIdAsync(userId);
            if (user == null)
                throw new KeyNotFoundException($"User with ID {userId} not found.");

            var invoices = await _unitOfWork.Invoices.GetQueryable()
                .Where(i => i.UserId == userId && i.CreatedAt >= startDate && i.CreatedAt <= endDate)
                .ToListAsync();

            return CalculateInvoiceStats(invoices, userId, user.FullName);
        }

        #endregion

        #region Property-Based Retrieval

        public async Task<PagedResultDto<InvoiceDto>> GetInvoicesByPropertyAsync(Guid propertyId, InvoiceFilterDto filter)
        {
            filter.PropertyId = propertyId;
            return await GetFilteredInvoicesAsync(filter);
        }

        public async Task<IEnumerable<InvoiceDto>> GetPropertyInvoicesByStatusAsync(Guid propertyId, string status)
        {
            var invoices = await _unitOfWork.Invoices.GetQueryable()
                .Include(i => i.User)
                .Include(i => i.Property)
                .Include(i => i.InvoiceItems)
                .Where(i => i.PropertyId == propertyId && i.Status == status)
                .OrderByDescending(i => i.CreatedAt)
                .ToListAsync();

            return invoices.Select(MapToInvoiceDto);
        }

        public async Task<IEnumerable<InvoiceDto>> GetPropertyInvoicesByTypeAsync(Guid propertyId, string type)
        {
            var invoices = await _unitOfWork.Invoices.GetQueryable()
                .Include(i => i.User)
                .Include(i => i.Property)
                .Include(i => i.InvoiceItems)
                .Where(i => i.PropertyId == propertyId && i.Type == type)
                .OrderByDescending(i => i.CreatedAt)
                .ToListAsync();

            return invoices.Select(MapToInvoiceDto);
        }

        public async Task<PropertyInvoiceStatsDto> GetPropertyInvoiceStatsAsync(Guid propertyId)
        {
            return await GetPropertyInvoiceStatsByDateRangeAsync(propertyId, DateTime.MinValue, DateTime.MaxValue);
        }

        public async Task<PropertyInvoiceStatsDto> GetPropertyInvoiceStatsByDateRangeAsync(Guid propertyId, DateTime startDate, DateTime endDate)
        {
            var property = await _unitOfWork.Properties.GetByIdAsync(propertyId);
            if (property == null)
                throw new KeyNotFoundException($"Property with ID {propertyId} not found.");

            var invoices = await _unitOfWork.Invoices.GetQueryable()
                .Where(i => i.PropertyId == propertyId && i.CreatedAt >= startDate && i.CreatedAt <= endDate)
                .ToListAsync();

            return CalculatePropertyInvoiceStats(invoices, propertyId, property.Name, property.Address);
        }

        public async Task<decimal> GetPropertyTotalSpentAsync(Guid propertyId)
        {
            var completedStatus = FunctionHelper.GetEnumDescription(EnumValues.InvoiceStatus.COMPLETED);
            return await _unitOfWork.Invoices.GetQueryable()
                .Where(i => i.PropertyId == propertyId && i.Status == completedStatus)
                .SumAsync(i => i.TotalAmount);
        }

        public async Task<IEnumerable<InvoiceDto>> GetPropertyInvoiceHistoryAsync(Guid propertyId)
        {
            var invoices = await _unitOfWork.Invoices.GetQueryable()
                .Include(i => i.User)
                .Include(i => i.Property)
                .Include(i => i.InvoiceItems)
                .Where(i => i.PropertyId == propertyId)
                .OrderByDescending(i => i.CreatedAt)
                .ToListAsync();

            return invoices.Select(MapToInvoiceDto);
        }

        #endregion

        #region Advanced Query Methods

        public async Task<PagedResultDto<InvoiceDto>> GetInvoicesByUserAndPropertyAsync(Guid userId, Guid propertyId, InvoiceFilterDto filter)
        {
            filter.UserId = userId;
            filter.PropertyId = propertyId;
            return await GetFilteredInvoicesAsync(filter);
        }

        public async Task<IEnumerable<InvoiceDto>> GetPendingInvoicesAsync(Guid? userId = null, Guid? propertyId = null)
        {
            var pendingStatus = FunctionHelper.GetEnumDescription(EnumValues.InvoiceStatus.PENDING);
            var query = _unitOfWork.Invoices.GetQueryable()
                .Include(i => i.User)
                .Include(i => i.Property)
                .Include(i => i.InvoiceItems)
                .Where(i => i.Status == pendingStatus);

            if (userId.HasValue)
                query = query.Where(i => i.UserId == userId.Value);

            if (propertyId.HasValue)
                query = query.Where(i => i.PropertyId == propertyId.Value);

            var invoices = await query.OrderByDescending(i => i.CreatedAt).ToListAsync();
            return invoices.Select(MapToInvoiceDto);
        }

        public async Task<IEnumerable<InvoiceDto>> GetOverdueInvoicesAsync(int daysOverdue = 30)
        {
            var cutoffDate = DateTime.UtcNow.AddDays(-daysOverdue);
            var pendingStatus = FunctionHelper.GetEnumDescription(EnumValues.InvoiceStatus.PENDING);

            var invoices = await _unitOfWork.Invoices.GetQueryable()
                .Include(i => i.User)
                .Include(i => i.Property)
                .Include(i => i.InvoiceItems)
                .Where(i => i.Status == pendingStatus && i.CreatedAt <= cutoffDate)
                .OrderByDescending(i => i.CreatedAt)
                .ToListAsync();

            return invoices.Select(MapToInvoiceDto);
        }

        public async Task<PagedResultDto<InvoiceDto>> SearchInvoicesAsync(string searchTerm, InvoiceFilterDto filter)
        {
            filter.SearchTerm = searchTerm;
            return await GetFilteredInvoicesAsync(filter);
        }

        #endregion

        #region Status Management

        public async Task<bool> MarkInvoiceAsPaidAsync(Guid id, Guid userId)
        {
            return await UpdateInvoiceStatusAsync(id, FunctionHelper.GetEnumDescription(EnumValues.InvoiceStatus.COMPLETED), userId);
        }

        public async Task<bool> MarkInvoiceAsFailedAsync(Guid id, Guid userId, string reason)
        {
            var invoice = await _unitOfWork.Invoices.GetByIdAsync(id);
            if (invoice == null)
                return false;

            if (invoice.UserId != userId)
                throw new UnauthorizedAccessException("You don't have permission to update this invoice.");

            invoice.Status = FunctionHelper.GetEnumDescription(EnumValues.InvoiceStatus.FAILED);
            invoice.Note = string.IsNullOrEmpty(invoice.Note) ? reason : $"{invoice.Note}; Failed: {reason}";

            _unitOfWork.Invoices.Update(invoice);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        public async Task<bool> CancelInvoiceAsync(Guid id, Guid userId, string reason)
        {
            var invoice = await _unitOfWork.Invoices.GetByIdAsync(id);
            if (invoice == null)
                return false;

            if (invoice.UserId != userId)
                throw new UnauthorizedAccessException("You don't have permission to update this invoice.");

            // Only allow cancellation of pending invoices
            if (invoice.Status != FunctionHelper.GetEnumDescription(EnumValues.InvoiceStatus.PENDING))
                throw new InvalidOperationException("Only pending invoices can be cancelled.");

            invoice.Status = FunctionHelper.GetEnumDescription(EnumValues.InvoiceStatus.CANCELLED);
            invoice.Note = string.IsNullOrEmpty(invoice.Note) ? reason : $"{invoice.Note}; Cancelled: {reason}";

            _unitOfWork.Invoices.Update(invoice);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        public async Task<bool> UpdateInvoiceStatusAsync(Guid id, string status, Guid userId)
        {
            var invoice = await _unitOfWork.Invoices.GetByIdAsync(id);
            if (invoice == null)
                return false;

            if (invoice.UserId != userId)
                throw new UnauthorizedAccessException("You don't have permission to update this invoice.");

            invoice.Status = status;

            // Set PaidAt if marking as completed
            if (status == FunctionHelper.GetEnumDescription(EnumValues.InvoiceStatus.COMPLETED))
                invoice.PaidAt = DateTime.UtcNow;

            _unitOfWork.Invoices.Update(invoice);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        #endregion

        #region Invoice Items Management

        public async Task<IEnumerable<InvoiceItemDto>> GetInvoiceItemsAsync(Guid invoiceId)
        {
            var items = await _unitOfWork.InvoiceItems.GetQueryable()
                .Where(ii => ii.InvoiceId == invoiceId)
                .ToListAsync();

            return _mapper.Map<IEnumerable<InvoiceItemDto>>(items);
        }

        public async Task<InvoiceItemDto> AddInvoiceItemAsync(Guid invoiceId, CreateInvoiceItemDto dto, Guid userId)
        {
            var invoice = await _unitOfWork.Invoices.GetByIdAsync(invoiceId);
            if (invoice == null)
                throw new KeyNotFoundException($"Invoice with ID {invoiceId} not found.");

            if (invoice.UserId != userId)
                throw new UnauthorizedAccessException("You don't have permission to modify this invoice.");

            var item = new InvoiceItem
            {
                InvoiceId = invoiceId,
                ItemType = FunctionHelper.GetEnumDescription(dto.ItemType),
                Description = dto.Description,
                Amount = dto.Amount
            };

            await _unitOfWork.InvoiceItems.AddAsync(item);

            // Update invoice total amount
            invoice.TotalAmount += dto.Amount;
            _unitOfWork.Invoices.Update(invoice);

            await _unitOfWork.SaveChangesAsync();

            return _mapper.Map<InvoiceItemDto>(item);
        }

        public async Task<bool> UpdateInvoiceItemAsync(Guid itemId, CreateInvoiceItemDto dto, Guid userId)
        {
            var item = await _unitOfWork.InvoiceItems.GetQueryable()
                .Include(ii => ii.Invoice)
                .FirstOrDefaultAsync(ii => ii.Id == itemId);

            if (item == null)
                return false;

            if (item.Invoice?.UserId != userId)
                throw new UnauthorizedAccessException("You don't have permission to modify this invoice item.");

            var oldAmount = item.Amount;

            item.ItemType = FunctionHelper.GetEnumDescription(dto.ItemType);
            item.Description = dto.Description;
            item.Amount = dto.Amount;

            _unitOfWork.InvoiceItems.Update(item);

            // Update invoice total amount
            if (item.Invoice != null)
            {
                item.Invoice.TotalAmount = item.Invoice.TotalAmount - oldAmount + dto.Amount;
                _unitOfWork.Invoices.Update(item.Invoice);
            }

            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        public async Task<bool> DeleteInvoiceItemAsync(Guid itemId, Guid userId)
        {
            var item = await _unitOfWork.InvoiceItems.GetQueryable()
                .Include(ii => ii.Invoice)
                .FirstOrDefaultAsync(ii => ii.Id == itemId);

            if (item == null)
                return false;

            if (item.Invoice?.UserId != userId)
                throw new UnauthorizedAccessException("You don't have permission to modify this invoice item.");

            _unitOfWork.InvoiceItems.Remove(item);

            // Update invoice total amount
            if (item.Invoice != null)
            {
                item.Invoice.TotalAmount -= item.Amount;
                _unitOfWork.Invoices.Update(item.Invoice);
            }

            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        #endregion

        #region Helper Methods

        private async Task<PagedResultDto<InvoiceDto>> GetFilteredInvoicesAsync(InvoiceFilterDto filter)
        {
            var query = _unitOfWork.Invoices.GetQueryable()
                .Include(i => i.User)
                .Include(i => i.Property)
                .Include(i => i.InvoiceItems)
                .AsQueryable();

            // Apply filters
            if (filter.UserId.HasValue)
                query = query.Where(i => i.UserId == filter.UserId.Value);

            if (filter.PropertyId.HasValue)
                query = query.Where(i => i.PropertyId == filter.PropertyId.Value);

            if (!string.IsNullOrEmpty(filter.Status))
                query = query.Where(i => i.Status == filter.Status);

            if (!string.IsNullOrEmpty(filter.Type))
                query = query.Where(i => i.Type == filter.Type);

            if (filter.StartDate.HasValue)
                query = query.Where(i => i.CreatedAt >= filter.StartDate.Value);

            if (filter.EndDate.HasValue)
                query = query.Where(i => i.CreatedAt <= filter.EndDate.Value);

            if (filter.MinAmount.HasValue)
                query = query.Where(i => i.TotalAmount >= filter.MinAmount.Value);

            if (filter.MaxAmount.HasValue)
                query = query.Where(i => i.TotalAmount <= filter.MaxAmount.Value);

            if (!string.IsNullOrEmpty(filter.SearchTerm))
            {
                var searchTerm = filter.SearchTerm.ToLower();
                query = query.Where(i =>
                    i.Note != null && i.Note.ToLower().Contains(searchTerm) ||
                    i.User != null && i.User.FullName.ToLower().Contains(searchTerm) ||
                    i.Property != null && i.Property.Name.ToLower().Contains(searchTerm));
            }

            // Apply sorting
            query = filter.SortBy.ToLower() switch
            {
                "totalamount" => filter.SortDescending ? query.OrderByDescending(i => i.TotalAmount) : query.OrderBy(i => i.TotalAmount),
                "status" => filter.SortDescending ? query.OrderByDescending(i => i.Status) : query.OrderBy(i => i.Status),
                "type" => filter.SortDescending ? query.OrderByDescending(i => i.Type) : query.OrderBy(i => i.Type),
                "paidat" => filter.SortDescending ? query.OrderByDescending(i => i.PaidAt) : query.OrderBy(i => i.PaidAt),
                _ => filter.SortDescending ? query.OrderByDescending(i => i.CreatedAt) : query.OrderBy(i => i.CreatedAt)
            };

            // Get total count
            var totalCount = await query.CountAsync();

            // Apply pagination
            var invoices = await query
                .Skip((filter.Page - 1) * filter.PageSize)
                .Take(filter.PageSize)
                .ToListAsync();

            var invoiceDtos = invoices.Select(MapToInvoiceDto).ToList();

            return new PagedResultDto<InvoiceDto>
            {
                Items = invoiceDtos,
                TotalCount = totalCount,
                CurrentPage = filter.Page,
                PageSize = filter.PageSize,
                PageCount = (int)Math.Ceiling((double)totalCount / filter.PageSize)
            };
        }

        private InvoiceDto MapToInvoiceDto(Invoice invoice)
        {
            return new InvoiceDto
            {
                Id = invoice.Id,
                UserId = invoice.UserId,
                PropertyId = invoice.PropertyId,
                Type = invoice.Type,
                TotalAmount = invoice.TotalAmount,
                Status = invoice.Status,
                Note = invoice.Note,
                CreatedAt = invoice.CreatedAt,
                PaidAt = invoice.PaidAt,
                UserName = invoice.User?.FullName,
                UserEmail = invoice.User?.Email,
                PropertyTitle = invoice.Property?.Name,
                PropertyAddress = invoice.Property?.Address,
                InvoiceItems = _mapper.Map<ICollection<InvoiceItemDto>>(invoice.InvoiceItems)
            };
        }

        private InvoiceStatsDto CalculateInvoiceStats(List<Invoice> invoices, Guid userId, string userName)
        {
            var completedStatus = FunctionHelper.GetEnumDescription(EnumValues.InvoiceStatus.COMPLETED);
            var pendingStatus = FunctionHelper.GetEnumDescription(EnumValues.InvoiceStatus.PENDING);
            var failedStatus = FunctionHelper.GetEnumDescription(EnumValues.InvoiceStatus.FAILED);
            var cancelledStatus = FunctionHelper.GetEnumDescription(EnumValues.InvoiceStatus.CANCELLED);

            return new InvoiceStatsDto
            {
                UserId = userId,
                UserName = userName,
                TotalSpent = invoices.Where(i => i.Status == completedStatus).Sum(i => i.TotalAmount),
                PendingAmount = invoices.Where(i => i.Status == pendingStatus).Sum(i => i.TotalAmount),
                CompletedAmount = invoices.Where(i => i.Status == completedStatus).Sum(i => i.TotalAmount),
                FailedAmount = invoices.Where(i => i.Status == failedStatus).Sum(i => i.TotalAmount),
                TotalInvoices = invoices.Count,
                PendingInvoices = invoices.Count(i => i.Status == pendingStatus),
                CompletedInvoices = invoices.Count(i => i.Status == completedStatus),
                FailedInvoices = invoices.Count(i => i.Status == failedStatus),
                CancelledInvoices = invoices.Count(i => i.Status == cancelledStatus),
                LastInvoiceDate = invoices.Any() ? invoices.Max(i => i.CreatedAt) : null,
                SpentByType = invoices.Where(i => i.Status == completedStatus)
                    .GroupBy(i => i.Type)
                    .ToDictionary(g => g.Key, g => g.Sum(i => (decimal)i.TotalAmount)),
                InvoiceCountByType = invoices
                    .GroupBy(i => i.Type)
                    .ToDictionary(g => g.Key, g => g.Count())
            };
        }

        private PropertyInvoiceStatsDto CalculatePropertyInvoiceStats(List<Invoice> invoices, Guid propertyId, string propertyTitle, string propertyAddress)
        {
            var completedStatus = FunctionHelper.GetEnumDescription(EnumValues.InvoiceStatus.COMPLETED);
            var pendingStatus = FunctionHelper.GetEnumDescription(EnumValues.InvoiceStatus.PENDING);
            var failedStatus = FunctionHelper.GetEnumDescription(EnumValues.InvoiceStatus.FAILED);
            var cancelledStatus = FunctionHelper.GetEnumDescription(EnumValues.InvoiceStatus.CANCELLED);

            return new PropertyInvoiceStatsDto
            {
                PropertyId = propertyId,
                PropertyTitle = propertyTitle,
                PropertyAddress = propertyAddress,
                TotalSpent = invoices.Where(i => i.Status == completedStatus).Sum(i => i.TotalAmount),
                PendingAmount = invoices.Where(i => i.Status == pendingStatus).Sum(i => i.TotalAmount),
                CompletedAmount = invoices.Where(i => i.Status == completedStatus).Sum(i => i.TotalAmount),
                FailedAmount = invoices.Where(i => i.Status == failedStatus).Sum(i => i.TotalAmount),
                TotalInvoices = invoices.Count,
                PendingInvoices = invoices.Count(i => i.Status == pendingStatus),
                CompletedInvoices = invoices.Count(i => i.Status == completedStatus),
                FailedInvoices = invoices.Count(i => i.Status == failedStatus),
                CancelledInvoices = invoices.Count(i => i.Status == cancelledStatus),
                LastInvoiceDate = invoices.Any() ? invoices.Max(i => i.CreatedAt) : null,
                SpentByType = invoices.Where(i => i.Status == completedStatus)
                    .GroupBy(i => i.Type)
                    .ToDictionary(g => g.Key, g => g.Sum(i => (decimal)i.TotalAmount)),
                InvoiceCountByType = invoices
                    .GroupBy(i => i.Type)
                    .ToDictionary(g => g.Key, g => g.Count())
            };
        }

        #endregion
    }
}
