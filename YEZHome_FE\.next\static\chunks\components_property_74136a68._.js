(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/components/property/MapPropertyPopup.jsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "MapPropertyPopup": (()=>MapPropertyPopup)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$external$2d$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ExternalLinkIcon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/external-link.js [app-client] (ecmascript) <export default as ExternalLinkIcon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.js [app-client] (ecmascript)");
"use client";
;
;
;
;
const MapPropertyPopup = ({ tCommon, property, onViewDetails })=>{
    const handleViewDetails = (e)=>{
        e.stopPropagation();
        if (onViewDetails) {
            onViewDetails(property);
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "w-[180px] xs:w-[220px] sm:w-64 bg-white rounded-lg overflow-hidden shadow-lg",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "relative h-24 xs:h-28 sm:h-32 w-full overflow-hidden",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        src: property.imageUrl || "/placeholder.svg?height=128&width=256",
                        alt: property.name,
                        fill: true,
                        className: "object-cover",
                        sizes: "256px"
                    }, void 0, false, {
                        fileName: "[project]/components/property/MapPropertyPopup.jsx",
                        lineNumber: 20,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute top-2 left-2 bg-linear-to-r from-red-500 to-rose-500 text-white px-2 py-0.5 rounded-full text-xs font-medium",
                        children: tCommon(`propertyPostType_${property.postType}`)
                    }, void 0, false, {
                        fileName: "[project]/components/property/MapPropertyPopup.jsx",
                        lineNumber: 29,
                        columnNumber: 9
                    }, this),
                    property.isHighlighted && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute top-2 right-2 bg-black/60 text-white px-1.5 py-0.5 rounded text-[10px] font-medium",
                        children: tCommon(`highlight_status`)
                    }, void 0, false, {
                        fileName: "[project]/components/property/MapPropertyPopup.jsx",
                        lineNumber: 35,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/property/MapPropertyPopup.jsx",
                lineNumber: 19,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "p-3",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex justify-between items-center mb-1",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-sm xs:text-base font-bold text-gray-800",
                                children: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatCurrency"])(property.price)
                            }, void 0, false, {
                                fileName: "[project]/components/property/MapPropertyPopup.jsx",
                                lineNumber: 45,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-xs text-gray-500 bg-gray-100 px-2 py-0.5 rounded",
                                children: property.propertyType
                            }, void 0, false, {
                                fileName: "[project]/components/property/MapPropertyPopup.jsx",
                                lineNumber: 48,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/property/MapPropertyPopup.jsx",
                        lineNumber: 44,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "text-sm font-medium text-gray-800 mb-1 line-clamp-2",
                        children: property.name
                    }, void 0, false, {
                        fileName: "[project]/components/property/MapPropertyPopup.jsx",
                        lineNumber: 54,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex justify-between gap-1 text-xs text-gray-500 mb-3",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: [
                                    property.area || "__",
                                    " m²"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/components/property/MapPropertyPopup.jsx",
                                lineNumber: 57,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: [
                                    property.rooms || "__",
                                    " PN"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/components/property/MapPropertyPopup.jsx",
                                lineNumber: 58,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: [
                                    property.toilets || "__",
                                    " PT"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/components/property/MapPropertyPopup.jsx",
                                lineNumber: 59,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/property/MapPropertyPopup.jsx",
                        lineNumber: 56,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: handleViewDetails,
                        className: "w-full bg-linear-to-r from-blue-500 to-teal-500 text-white py-1.5 sm:py-2 rounded text-xs font-medium hover:opacity-90 transition-opacity flex items-center justify-center",
                        children: [
                            "Xem chi tiết",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$external$2d$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ExternalLinkIcon$3e$__["ExternalLinkIcon"], {
                                size: 12,
                                className: "ml-1"
                            }, void 0, false, {
                                fileName: "[project]/components/property/MapPropertyPopup.jsx",
                                lineNumber: 68,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/components/property/MapPropertyPopup.jsx",
                        lineNumber: 63,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/components/property/MapPropertyPopup.jsx",
                lineNumber: 42,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/components/property/MapPropertyPopup.jsx",
        lineNumber: 17,
        columnNumber: 5
    }, this);
};
_c = MapPropertyPopup;
var _c;
__turbopack_context__.k.register(_c, "MapPropertyPopup");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/property/HomeMap.jsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$goongmaps$2f$goong$2d$js$2f$dist$2f$goong$2d$js$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@goongmaps/goong-js/dist/goong-js.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$components$2f$property$2f$MapPropertyPopup$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/components/property/MapPropertyPopup.jsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2d$dom$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react-dom/client.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
const isBrowser = "object" !== "undefined";
const HomeMap = ({ markers = [], center, onBoundsChange, onViewDetails, activePropertyId = null })=>{
    _s();
    const mapContainerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const mapRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const markerRootsRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])([]);
    const handleMoveEndRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const currentOpenPopupRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const tCommon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])("Common");
    // --- Effect khai báo instance cho bản đồ ---
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "HomeMap.useEffect": ()=>{
            if (!isBrowser || !mapContainerRef.current || mapRef.current) {
                return;
            }
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$goongmaps$2f$goong$2d$js$2f$dist$2f$goong$2d$js$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].accessToken = `${"TURBOPACK compile-time value", "81Fs1nNxdnePjpwvhjbtlVJCm12lOfTpmOmYtWR4"}`;
            const goongMap = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$goongmaps$2f$goong$2d$js$2f$dist$2f$goong$2d$js$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Map({
                container: mapContainerRef.current,
                style: "https://tiles.goong.io/assets/goong_map_highlight.json",
                center: [
                    center.longitude,
                    center.latitude
                ],
                zoom: 15
            });
            mapRef.current = goongMap;
            goongMap.on("load", {
                "HomeMap.useEffect": ()=>{
                    const currentBounds = goongMap.getBounds();
                    if (onBoundsChange) {
                        onBoundsChange(currentBounds);
                    }
                }
            }["HomeMap.useEffect"]);
            return ({
                "HomeMap.useEffect": ()=>{
                    if (mapRef.current) {
                        if (handleMoveEndRef.current) {
                            mapRef.current.off("moveend", handleMoveEndRef.current);
                        }
                        mapRef.current.remove();
                        mapRef.current = null;
                    }
                }
            })["HomeMap.useEffect"];
        }
    }["HomeMap.useEffect"], [
        center,
        onBoundsChange
    ]);
    // --- Effect xử lý sự kiện moveend của bản đồ ---
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "HomeMap.useEffect": ()=>{
            const map = mapRef.current;
            if (!map || !center || !onBoundsChange) {
                return;
            }
            const currentCenter = map.getCenter();
            const centerHasChanged = currentCenter.lat !== center.latitude || currentCenter.lng !== center.longitude;
            if (centerHasChanged) {
                map.flyTo({
                    center: [
                        center.longitude,
                        center.latitude
                    ],
                    essential: true
                });
            }
            handleMoveEndRef.current = ({
                "HomeMap.useEffect": (event)=>{
                    const currentBounds = event.target.getBounds();
                    if (onBoundsChange) {
                        onBoundsChange(currentBounds);
                    }
                }
            })["HomeMap.useEffect"];
            map.off("moveend", handleMoveEndRef.current);
            map.on("moveend", handleMoveEndRef.current);
            return ({
                "HomeMap.useEffect": ()=>{
                    if (mapRef.current && handleMoveEndRef.current) {
                        mapRef.current.off("moveend", handleMoveEndRef.current);
                    }
                }
            })["HomeMap.useEffect"];
        }
    }["HomeMap.useEffect"], [
        center,
        onBoundsChange,
        mapRef.current
    ]);
    // --- Effect xử lý Markers ---
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "HomeMap.useEffect": ()=>{
            const map = mapRef.current;
            if (!map || !markers) {
                markerRootsRef.current.forEach({
                    "HomeMap.useEffect": (item)=>{
                        setTimeout({
                            "HomeMap.useEffect": ()=>{
                                if (item.root) {
                                    try {
                                        item.root.unmount();
                                    } catch (e) {
                                        console.error(e);
                                    }
                                }
                                if (item.marker && item.marker.remove) {
                                    try {
                                        item.marker.remove();
                                    } catch (e) {
                                        console.error(e);
                                    }
                                }
                            }
                        }["HomeMap.useEffect"], 0);
                    }
                }["HomeMap.useEffect"]);
                markerRootsRef.current = [];
                return;
            }
            markerRootsRef.current.forEach({
                "HomeMap.useEffect": (item)=>{
                    setTimeout({
                        "HomeMap.useEffect": ()=>{
                            if (item.root) {
                                try {
                                    item.root.unmount();
                                } catch (e) {
                                    console.error(e);
                                }
                            }
                            if (item.marker && item.marker.remove) {
                                try {
                                    item.marker.remove();
                                } catch (e) {
                                    console.error(e);
                                }
                            }
                        }
                    }["HomeMap.useEffect"], 0);
                }
            }["HomeMap.useEffect"]);
            markerRootsRef.current = [];
            // Thêm markers mới nếu có
            if (markers && markers.length > 0) {
                markers.forEach({
                    "HomeMap.useEffect": (markerData, index)=>{
                        if (markerData.latitude !== undefined && markerData.longitude !== undefined) {
                            try {
                                // --- TẠO PHẦN TỬ DOM TÙY CHỈNH CHO MARKER ---
                                const el = document.createElement("div");
                                el.className = `custom-price-marker ${markerData.isHighlighted ? "highlighted" : ""}`;
                                el.innerHTML = `
                 <div class="price-bubble">${(0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatPriceShort"])(markerData.price)}</div>
                 <div class="price-arrow"></div>
            `;
                                var markerHeight = 50, markerRadius = 10, linearOffset = 25;
                                var popupOffsets = {
                                    top: [
                                        0,
                                        0
                                    ],
                                    "top-left": [
                                        0,
                                        0
                                    ],
                                    "top-right": [
                                        0,
                                        0
                                    ],
                                    bottom: [
                                        0,
                                        -markerHeight
                                    ],
                                    "bottom-left": [
                                        linearOffset,
                                        (markerHeight - markerRadius + linearOffset) * -1
                                    ],
                                    "bottom-right": [
                                        -linearOffset,
                                        (markerHeight - markerRadius + linearOffset) * -1
                                    ],
                                    left: [
                                        markerRadius,
                                        (markerHeight - markerRadius) * -1
                                    ],
                                    right: [
                                        -markerRadius,
                                        (markerHeight - markerRadius) * -1
                                    ]
                                };
                                const popupContainer = document.createElement("div");
                                const popup = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$goongmaps$2f$goong$2d$js$2f$dist$2f$goong$2d$js$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Popup({
                                    offset: popupOffsets,
                                    closeButton: false,
                                    closeOnClick: true
                                });
                                const root = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2d$dom$2f$client$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createRoot(popupContainer);
                                root.render(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$components$2f$property$2f$MapPropertyPopup$2e$jsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MapPropertyPopup"], {
                                    property: markerData,
                                    onViewDetails: onViewDetails,
                                    tCommon: tCommon
                                }, void 0, false, {
                                    fileName: "[project]/components/property/HomeMap.jsx",
                                    lineNumber: 163,
                                    columnNumber: 25
                                }, this));
                                popup.setDOMContent(popupContainer);
                                const marker = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$goongmaps$2f$goong$2d$js$2f$dist$2f$goong$2d$js$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Marker(el, {
                                    anchor: "bottom"
                                }).setLngLat([
                                    markerData.longitude,
                                    markerData.latitude
                                ]).setPopup(popup).addTo(map);
                                // --- LƯU TRỮ INSTANCE MARKER VÀ ROOT REACT ---
                                markerRootsRef.current.push({
                                    marker: marker,
                                    root: root,
                                    propertyId: markerData.id
                                });
                            } catch (markerError) {
                                console.error(`GoongMap Effect Markers: Lỗi khi tạo hoặc thêm marker ${index}:`, markerData, markerError); // <-- LOG LỖI TẠO/THÊM MARKER
                            }
                        }
                    }
                }["HomeMap.useEffect"]);
            }
            // --- Cleanup cho effect markers ---
            return ({
                "HomeMap.useEffect": ()=>{
                    markerRootsRef.current.forEach({
                        "HomeMap.useEffect": (item)=>{
                            setTimeout({
                                "HomeMap.useEffect": ()=>{
                                    if (item.root) {
                                        try {
                                            item.root.unmount();
                                        } catch (e) {
                                            console.error(e);
                                        }
                                    }
                                    if (item.marker && item.marker.remove) {
                                        try {
                                            item.marker.remove();
                                        } catch (e) {
                                            console.error(e);
                                        }
                                    }
                                }
                            }["HomeMap.useEffect"], 0);
                        }
                    }["HomeMap.useEffect"]);
                    markerRootsRef.current = [];
                }
            })["HomeMap.useEffect"];
        }
    }["HomeMap.useEffect"], [
        markers,
        mapRef.current
    ]); // Dependency: markers thay đổi HOẶC mapRef.current có giá trị
    // Effect to open/close popup based on activePropertyId prop
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "HomeMap.useEffect": ()=>{
            const map = mapRef.current;
            if (!map) {
                // Cleanup popup if map disappears while activeId is not null
                if (currentOpenPopupRef.current) {
                    currentOpenPopupRef.current.remove();
                    currentOpenPopupRef.current = null;
                }
                return;
            }
            // --- Bước 1: Đóng popup hiện đang được mở bởi effect này ---
            // Sử dụng ref để theo dõi popup đã mở trước đó
            if (currentOpenPopupRef.current) {
                try {
                    currentOpenPopupRef.current.remove(); // Đóng và xóa popup cũ
                } catch (e) {
                    console.error("Error removing previous popup:", e);
                }
                currentOpenPopupRef.current = null; // Reset ref
            }
            if (activePropertyId === null) {
                return;
            }
            // --- Bước 2: Tìm marker mục tiêu và mở popup của nó ---
            const activeMarkerItem = markerRootsRef.current.find({
                "HomeMap.useEffect.activeMarkerItem": (item)=>item.propertyId === activePropertyId
            }["HomeMap.useEffect.activeMarkerItem"]);
            if (activeMarkerItem && activeMarkerItem.marker) {
                const popupInstance = activeMarkerItem.marker.getPopup();
                if (popupInstance) {
                    if (!popupInstance.isOpen()) {
                        popupInstance.addTo(map); // Mở popup
                    }
                    currentOpenPopupRef.current = popupInstance; // <--- LƯU VÀO REF
                }
            }
            return ({
                "HomeMap.useEffect": ()=>{
                    if (currentOpenPopupRef.current) {
                        try {
                            currentOpenPopupRef.current.remove();
                        } catch (e) {
                            console.error("Error removing popup during cleanup:", e);
                        }
                        currentOpenPopupRef.current = null; // Reset ref
                    }
                }
            })["HomeMap.useEffect"]; // Cleanup handled by closing at start of effect
        }
    }["HomeMap.useEffect"], [
        activePropertyId,
        mapRef.current,
        markerRootsRef.current,
        markers
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `w-3/5 h-[calc(100vh-227px)] relative z-0 opacity-75 cursor-not-allowed`,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            ref: mapContainerRef,
            className: "w-full h-full home-map"
        }, void 0, false, {
            fileName: "[project]/components/property/HomeMap.jsx",
            lineNumber: 256,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/components/property/HomeMap.jsx",
        lineNumber: 255,
        columnNumber: 5
    }, this);
};
_s(HomeMap, "7bvaHbc9VhXGJdz7gya2LDNj4Rk=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"]
    ];
});
_c = HomeMap;
const __TURBOPACK__default__export__ = HomeMap;
var _c;
__turbopack_context__.k.register(_c, "HomeMap");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/property/HomeMap.jsx [app-client] (ecmascript, next/dynamic entry)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/components/property/HomeMap.jsx [app-client] (ecmascript)"));
}}),
}]);

//# sourceMappingURL=components_property_74136a68._.js.map