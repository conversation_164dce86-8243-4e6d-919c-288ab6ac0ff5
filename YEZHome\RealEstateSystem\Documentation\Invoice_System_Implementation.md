# Invoice System Implementation

## Overview

The Invoice System provides comprehensive invoice management functionality for the YEZ Home real estate platform. It enables users to create, manage, and track invoices related to property services such as new posts, renewals, and highlights.

## Database Schema

### Tables Created

#### 1. Invoices Table
```sql
CREATE TABLE "Invoices" (
    "Id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "UserId" UUID NOT NULL REFERENCES "AspNetUsers"("Id") ON DELETE CASCADE,
    "PropertyId" UUID NOT NULL REFERENCES "Properties"("Id") ON DELETE CASCADE,
    "Type" VARCHAR(50) NOT NULL, -- 'new_post', 'renew', 'highlight'
    "TotalAmount" INTEGER NOT NULL,
    "Status" VARCHAR(20) NOT NULL DEFAULT 'PENDING', -- 'PENDING', 'COMPLETED', 'FAILED', 'CANCELLED'
    "Note" TEXT,
    "CreatedAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "PaidAt" TIMESTAMP,
    "IsDeleted" BOOLEAN NOT NULL DEFAULT FALSE,
    "CreatedAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

#### 2. InvoiceItems Table
```sql
CREATE TABLE "InvoiceItems" (
    "Id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "InvoiceId" UUID NOT NULL REFERENCES "Invoices"("Id") ON DELETE CASCADE,
    "ItemType" VARCHAR(50) NOT NULL, -- 'new_post', 'renew', 'highlight'
    "Description" TEXT,
    "Amount" INTEGER NOT NULL,
    "IsDeleted" BOOLEAN NOT NULL DEFAULT FALSE,
    "CreatedAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

## Domain Layer

### Enums

#### InvoiceStatus
- `PENDING` - Invoice is awaiting payment
- `COMPLETED` - Invoice has been paid successfully
- `FAILED` - Invoice payment failed
- `CANCELLED` - Invoice was cancelled

#### InvoiceType
- `NEW_POST` - Invoice for creating a new property post
- `RENEW` - Invoice for renewing a property listing
- `HIGHLIGHT` - Invoice for highlighting a property

#### InvoiceItemType
- `NEW_POST` - Item for new property post
- `RENEW` - Item for property renewal
- `HIGHLIGHT` - Item for property highlighting

### Entities

#### Invoice Entity
```csharp
public class Invoice : BaseEntity
{
    public Guid UserId { get; set; }
    public Guid PropertyId { get; set; }
    public string Type { get; set; }
    public int TotalAmount { get; set; }
    public string Status { get; set; }
    public string? Note { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? PaidAt { get; set; }
    
    // Navigation properties
    public AppUser? User { get; set; }
    public Property? Property { get; set; }
    public ICollection<InvoiceItem> InvoiceItems { get; set; }
}
```

#### InvoiceItem Entity
```csharp
public class InvoiceItem : BaseEntity
{
    public Guid InvoiceId { get; set; }
    public string ItemType { get; set; }
    public string? Description { get; set; }
    public int Amount { get; set; }
    
    // Navigation properties
    public Invoice? Invoice { get; set; }
}
```

## Application Layer

### DTOs

#### Core DTOs
- `InvoiceDto` - Complete invoice information with navigation properties
- `CreateInvoiceDto` - Data for creating new invoices
- `UpdateInvoiceDto` - Data for updating existing invoices
- `InvoiceItemDto` - Invoice item information
- `CreateInvoiceItemDto` - Data for creating invoice items

#### Filter and Statistics DTOs
- `InvoiceFilterDto` - Filtering and pagination parameters
- `InvoiceStatsDto` - User invoice statistics
- `PropertyInvoiceStatsDto` - Property-specific invoice statistics

### Service Interface

#### IInvoiceService Methods

##### Core CRUD Operations
```csharp
Task<InvoiceDto> GetInvoiceByIdAsync(Guid id);
Task<InvoiceDto> CreateInvoiceAsync(CreateInvoiceDto dto, Guid userId);
Task<InvoiceDto> UpdateInvoiceAsync(Guid id, UpdateInvoiceDto dto, Guid userId);
Task<bool> DeleteInvoiceAsync(Guid id, Guid userId);
```

##### User-Based Retrieval
```csharp
Task<PagedResultDto<InvoiceDto>> GetInvoicesByUserAsync(Guid userId, InvoiceFilterDto filter);
Task<IEnumerable<InvoiceDto>> GetUserInvoicesByStatusAsync(Guid userId, string status);
Task<IEnumerable<InvoiceDto>> GetUserInvoicesByTypeAsync(Guid userId, string type);
Task<InvoiceStatsDto> GetUserInvoiceStatsAsync(Guid userId);
Task<InvoiceStatsDto> GetUserInvoiceStatsByDateRangeAsync(Guid userId, DateTime startDate, DateTime endDate);
```

##### Property-Based Retrieval
```csharp
Task<PagedResultDto<InvoiceDto>> GetInvoicesByPropertyAsync(Guid propertyId, InvoiceFilterDto filter);
Task<IEnumerable<InvoiceDto>> GetPropertyInvoicesByStatusAsync(Guid propertyId, string status);
Task<IEnumerable<InvoiceDto>> GetPropertyInvoicesByTypeAsync(Guid propertyId, string type);
Task<PropertyInvoiceStatsDto> GetPropertyInvoiceStatsAsync(Guid propertyId);
Task<PropertyInvoiceStatsDto> GetPropertyInvoiceStatsByDateRangeAsync(Guid propertyId, DateTime startDate, DateTime endDate);
Task<decimal> GetPropertyTotalSpentAsync(Guid propertyId);
Task<IEnumerable<InvoiceDto>> GetPropertyInvoiceHistoryAsync(Guid propertyId);
```

##### Advanced Query Methods
```csharp
Task<PagedResultDto<InvoiceDto>> GetInvoicesByUserAndPropertyAsync(Guid userId, Guid propertyId, InvoiceFilterDto filter);
Task<IEnumerable<InvoiceDto>> GetPendingInvoicesAsync(Guid? userId = null, Guid? propertyId = null);
Task<IEnumerable<InvoiceDto>> GetOverdueInvoicesAsync(int daysOverdue = 30);
Task<PagedResultDto<InvoiceDto>> SearchInvoicesAsync(string searchTerm, InvoiceFilterDto filter);
```

##### Status Management
```csharp
Task<bool> MarkInvoiceAsPaidAsync(Guid id, Guid userId);
Task<bool> MarkInvoiceAsFailedAsync(Guid id, Guid userId, string reason);
Task<bool> CancelInvoiceAsync(Guid id, Guid userId, string reason);
Task<bool> UpdateInvoiceStatusAsync(Guid id, string status, Guid userId);
```

##### Invoice Items Management
```csharp
Task<IEnumerable<InvoiceItemDto>> GetInvoiceItemsAsync(Guid invoiceId);
Task<InvoiceItemDto> AddInvoiceItemAsync(Guid invoiceId, CreateInvoiceItemDto dto, Guid userId);
Task<bool> UpdateInvoiceItemAsync(Guid itemId, CreateInvoiceItemDto dto, Guid userId);
Task<bool> DeleteInvoiceItemAsync(Guid itemId, Guid userId);
```

## API Layer

### InvoiceController Endpoints

#### Basic Invoice Operations

##### GET /api/invoice/{id}
Get invoice by ID
- **Authorization**: Required
- **Parameters**: `id` (Guid) - Invoice ID
- **Returns**: `InvoiceDto`

##### POST /api/invoice
Create new invoice
- **Authorization**: Required
- **Body**: `CreateInvoiceDto`
- **Returns**: `InvoiceDto`

##### PUT /api/invoice/{id}
Update invoice
- **Authorization**: Required (Owner only)
- **Parameters**: `id` (Guid) - Invoice ID
- **Body**: `UpdateInvoiceDto`
- **Returns**: `InvoiceDto`

##### DELETE /api/invoice/{id}
Delete invoice (pending invoices only)
- **Authorization**: Required (Owner only)
- **Parameters**: `id` (Guid) - Invoice ID
- **Returns**: `204 No Content`

#### User-Based Endpoints

##### GET /api/invoice/user/{userId}
Get user's invoices with filtering and pagination
- **Authorization**: Required (Owner or Admin)
- **Parameters**:
  - `userId` (Guid) - User ID
  - Query parameters from `InvoiceFilterDto`
- **Returns**: `PagedResultDto<InvoiceDto>`

##### GET /api/invoice/user/{userId}/status/{status}
Get user's invoices by status
- **Authorization**: Required (Owner or Admin)
- **Parameters**:
  - `userId` (Guid) - User ID
  - `status` (string) - Invoice status
- **Returns**: `IEnumerable<InvoiceDto>`

##### GET /api/invoice/user/{userId}/type/{type}
Get user's invoices by type
- **Authorization**: Required (Owner or Admin)
- **Parameters**:
  - `userId` (Guid) - User ID
  - `type` (string) - Invoice type
- **Returns**: `IEnumerable<InvoiceDto>`

##### GET /api/invoice/user/{userId}/stats
Get user's invoice statistics
- **Authorization**: Required (Owner or Admin)
- **Parameters**: `userId` (Guid) - User ID
- **Returns**: `InvoiceStatsDto`

##### GET /api/invoice/user/{userId}/stats/daterange
Get user's invoice statistics by date range
- **Authorization**: Required (Owner or Admin)
- **Parameters**:
  - `userId` (Guid) - User ID
  - `startDate` (DateTime) - Start date
  - `endDate` (DateTime) - End date
- **Returns**: `InvoiceStatsDto`

#### Property-Based Endpoints

##### GET /api/invoice/property/{propertyId}
Get property's invoices with filtering and pagination
- **Authorization**: Required
- **Parameters**:
  - `propertyId` (Guid) - Property ID
  - Query parameters from `InvoiceFilterDto`
- **Returns**: `PagedResultDto<InvoiceDto>`

##### GET /api/invoice/property/{propertyId}/status/{status}
Get property's invoices by status
- **Authorization**: Required
- **Parameters**:
  - `propertyId` (Guid) - Property ID
  - `status` (string) - Invoice status
- **Returns**: `IEnumerable<InvoiceDto>`

##### GET /api/invoice/property/{propertyId}/type/{type}
Get property's invoices by type
- **Authorization**: Required
- **Parameters**:
  - `propertyId` (Guid) - Property ID
  - `type` (string) - Invoice type
- **Returns**: `IEnumerable<InvoiceDto>`

##### GET /api/invoice/property/{propertyId}/stats
Get property's invoice statistics
- **Authorization**: Required
- **Parameters**: `propertyId` (Guid) - Property ID
- **Returns**: `PropertyInvoiceStatsDto`

##### GET /api/invoice/property/{propertyId}/stats/daterange
Get property's invoice statistics by date range
- **Authorization**: Required
- **Parameters**:
  - `propertyId` (Guid) - Property ID
  - `startDate` (DateTime) - Start date
  - `endDate` (DateTime) - End date
- **Returns**: `PropertyInvoiceStatsDto`

##### GET /api/invoice/property/{propertyId}/total-spent
Get total amount spent on property
- **Authorization**: Required
- **Parameters**: `propertyId` (Guid) - Property ID
- **Returns**: `decimal`

##### GET /api/invoice/property/{propertyId}/history
Get property's invoice history
- **Authorization**: Required
- **Parameters**: `propertyId` (Guid) - Property ID
- **Returns**: `IEnumerable<InvoiceDto>`

#### Advanced Query Endpoints

##### GET /api/invoice/user/{userId}/property/{propertyId}
Get invoices by user and property
- **Authorization**: Required (Owner or Admin)
- **Parameters**:
  - `userId` (Guid) - User ID
  - `propertyId` (Guid) - Property ID
  - Query parameters from `InvoiceFilterDto`
- **Returns**: `PagedResultDto<InvoiceDto>`

##### GET /api/invoice/pending
Get pending invoices
- **Authorization**: Required
- **Parameters**:
  - `userId` (Guid, optional) - Filter by user
  - `propertyId` (Guid, optional) - Filter by property
- **Returns**: `IEnumerable<InvoiceDto>`

##### GET /api/invoice/overdue
Get overdue invoices
- **Authorization**: Required (Admin only)
- **Parameters**: `daysOverdue` (int, default: 30) - Days overdue threshold
- **Returns**: `IEnumerable<InvoiceDto>`

##### GET /api/invoice/search
Search invoices
- **Authorization**: Required
- **Parameters**:
  - `searchTerm` (string) - Search term
  - Query parameters from `InvoiceFilterDto`
- **Returns**: `PagedResultDto<InvoiceDto>`

#### Status Management Endpoints

##### POST /api/invoice/{id}/mark-paid
Mark invoice as paid
- **Authorization**: Required (Owner only)
- **Parameters**: `id` (Guid) - Invoice ID
- **Returns**: `200 OK`

##### POST /api/invoice/{id}/mark-failed
Mark invoice as failed
- **Authorization**: Required (Owner only)
- **Parameters**: `id` (Guid) - Invoice ID
- **Body**: `string` - Failure reason
- **Returns**: `200 OK`

##### POST /api/invoice/{id}/cancel
Cancel invoice
- **Authorization**: Required (Owner only)
- **Parameters**: `id` (Guid) - Invoice ID
- **Body**: `string` - Cancellation reason
- **Returns**: `200 OK`

##### PUT /api/invoice/{id}/status
Update invoice status
- **Authorization**: Required (Owner only)
- **Parameters**: `id` (Guid) - Invoice ID
- **Body**: `string` - New status
- **Returns**: `200 OK`

#### Invoice Items Endpoints

##### GET /api/invoice/{id}/items
Get invoice items
- **Authorization**: Required
- **Parameters**: `id` (Guid) - Invoice ID
- **Returns**: `IEnumerable<InvoiceItemDto>`

##### POST /api/invoice/{id}/items
Add invoice item
- **Authorization**: Required (Owner only)
- **Parameters**: `id` (Guid) - Invoice ID
- **Body**: `CreateInvoiceItemDto`
- **Returns**: `InvoiceItemDto`

##### PUT /api/invoice/items/{itemId}
Update invoice item
- **Authorization**: Required (Owner only)
- **Parameters**: `itemId` (Guid) - Invoice item ID
- **Body**: `CreateInvoiceItemDto`
- **Returns**: `200 OK`

##### DELETE /api/invoice/items/{itemId}
Delete invoice item
- **Authorization**: Required (Owner only)
- **Parameters**: `itemId` (Guid) - Invoice item ID
- **Returns**: `204 No Content`

## Usage Examples

### Creating an Invoice

```csharp
var createInvoiceDto = new CreateInvoiceDto
{
    PropertyId = propertyId,
    Type = InvoiceType.HIGHLIGHT,
    TotalAmount = 50000,
    Note = "Highlight property for 30 days",
    InvoiceItems = new List<CreateInvoiceItemDto>
    {
        new CreateInvoiceItemDto
        {
            ItemType = InvoiceItemType.HIGHLIGHT,
            Description = "30-day highlight service",
            Amount = 50000
        }
    }
};

var invoice = await _invoiceService.CreateInvoiceAsync(createInvoiceDto, userId);
```

### Getting User Invoice Statistics

```csharp
var stats = await _invoiceService.GetUserInvoiceStatsAsync(userId);
Console.WriteLine($"Total Spent: {stats.TotalSpent}");
Console.WriteLine($"Pending Amount: {stats.PendingAmount}");
Console.WriteLine($"Total Invoices: {stats.TotalInvoices}");
```

### Filtering Property Invoices

```csharp
var filter = new InvoiceFilterDto
{
    Status = "COMPLETED",
    StartDate = DateTime.Now.AddMonths(-3),
    EndDate = DateTime.Now,
    Page = 1,
    PageSize = 20,
    SortBy = "CreatedAt",
    SortDescending = true
};

var invoices = await _invoiceService.GetInvoicesByPropertyAsync(propertyId, filter);
```

### Marking Invoice as Paid

```csharp
var success = await _invoiceService.MarkInvoiceAsPaidAsync(invoiceId, userId);
if (success)
{
    Console.WriteLine("Invoice marked as paid successfully");
}
```

## Integration Points

### Property System Integration
- Invoices are linked to properties via `PropertyId`
- Property owners can view all invoices related to their properties
- Property analytics can include invoice spending data

### User System Integration
- Invoices are associated with users via `UserId`
- Users can view their complete invoice history
- User dashboard can display invoice statistics

### Wallet System Integration
- Potential integration for automatic payment processing
- Invoice status updates when wallet transactions complete
- Transaction linking through `TransactionId` (future enhancement)

### Analytics Integration
- Property spending analytics using invoice data
- User spending patterns and behavior analysis
- Revenue tracking and financial reporting

## Performance Considerations

### Database Optimization
- Indexes on frequently queried columns (`UserId`, `PropertyId`, `Status`, `CreatedAt`)
- Query filters for soft-deleted records
- Efficient pagination using skip/take patterns

### Caching Strategy
- Consider caching frequently accessed invoice statistics
- Cache property total spent amounts
- Cache user invoice counts and totals

### Query Optimization
- Use `Include()` statements judiciously to avoid N+1 queries
- Implement projection for list views to reduce data transfer
- Use async/await patterns throughout for better scalability

## Security Features

### Authorization
- Users can only access their own invoices (unless Admin)
- Property-based access control
- Role-based access for administrative functions

### Data Validation
- Input validation on all DTOs
- Business rule validation in service layer
- Enum validation for status and type fields

### Audit Trail
- Comprehensive logging of all invoice operations
- Status change tracking with reasons
- User action logging for security monitoring

## Error Handling

### Common Error Scenarios
- `KeyNotFoundException` - Invoice/Property/User not found
- `UnauthorizedAccessException` - Access denied
- `InvalidOperationException` - Business rule violations
- `ValidationException` - Input validation failures

### HTTP Status Codes
- `200 OK` - Successful operations
- `201 Created` - Successful creation
- `204 No Content` - Successful deletion
- `400 Bad Request` - Validation errors
- `401 Unauthorized` - Authentication required
- `403 Forbidden` - Access denied
- `404 Not Found` - Resource not found
- `500 Internal Server Error` - Server errors

## Future Enhancements

### Planned Features
- Automatic invoice generation for property services
- Payment gateway integration
- Invoice templates and customization
- Bulk invoice operations
- Invoice export functionality (PDF, Excel)
- Recurring invoice support
- Invoice approval workflows

### Integration Opportunities
- Email notifications for invoice status changes
- SMS notifications for overdue invoices
- Integration with accounting systems
- Mobile app support
- Real-time invoice status updates via SignalR

## Conclusion

The Invoice System provides a robust, scalable solution for managing property-related invoices in the YEZ Home platform. It follows established patterns from the existing codebase, implements comprehensive security measures, and provides extensive functionality for both users and administrators.

The system is designed for high performance with proper indexing, caching strategies, and optimized queries. It integrates seamlessly with existing systems while providing a foundation for future enhancements and integrations.
