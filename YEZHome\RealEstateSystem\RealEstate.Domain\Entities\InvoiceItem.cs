using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace RealEstate.Domain.Entities
{
    public class InvoiceItem : BaseEntity
    {
        [Required]
        public Guid InvoiceId { get; set; }
        
        [Required]
        [StringLength(50)]
        public string ItemType { get; set; } // property spending type (new_post, renew, highlight)
        
        public string? Description { get; set; }
        
        [Required]
        public int Amount { get; set; }
        
        // Navigation properties
        [ForeignKey("InvoiceId")]
        public Invoice? Invoice { get; set; }
    }
}
